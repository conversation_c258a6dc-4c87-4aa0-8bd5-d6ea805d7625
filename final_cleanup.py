#!/usr/bin/env python3
"""
Final cleanup script to fix all remaining Flake8 issues
"""

def fix_remaining_issues():
    """Fix all remaining line length issues"""
    with open('app_monitor.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Fix specific long lines
    for i, line in enumerate(lines):
        # Fix line 108: dashboard_data
        if 'dashboard_data = get_safe_data(lambda: backend.get_dashboard_data(), {})' in line:
            indent = len(line) - len(line.lstrip())
            lines[i] = ' ' * indent + 'dashboard_data = get_safe_data(\n'
            lines.insert(i + 1, ' ' * (indent + 4) + 'lambda: backend.get_dashboard_data(), {}\n')
            lines.insert(i + 2, ' ' * indent + ')\n')
        
        # Fix line 125: alerts.get_alerts
        elif 'lambda: backend.alerts.get_alerts(acknowledged=False, limit=10), []' in line:
            indent = len(line) - len(line.lstrip())
            lines[i] = ' ' * (indent + 4) + 'lambda: backend.alerts.get_alerts(\n'
            lines.insert(i + 1, ' ' * (indent + 8) + 'acknowledged=False, limit=10\n')
            lines.insert(i + 2, ' ' * (indent + 4) + '),\n')
            lines.insert(i + 3, ' ' * (indent + 4) + '[]\n')
        
        # Fix other long lines by breaking them appropriately
        elif len(line.rstrip()) > 79:
            # Handle specific patterns
            if 'get_safe_data(lambda:' in line and ', [])' in line:
                # Break lambda calls
                parts = line.split('get_safe_data(')
                if len(parts) == 2:
                    indent = len(parts[0])
                    lambda_part = parts[1].split(', []')[0]
                    lines[i] = parts[0] + 'get_safe_data(\n'
                    lines.insert(i + 1, ' ' * (indent + 4) + lambda_part + ',\n')
                    lines.insert(i + 2, ' ' * (indent + 4) + '[]\n')
                    lines.insert(i + 3, ' ' * indent + ')\n')
            
            elif 'system_health = {' in line and 'Legacy Mode' in line:
                # Break dictionary definitions
                indent = len(line) - len(line.lstrip())
                lines[i] = ' ' * indent + 'system_health = {\n'
                lines.insert(i + 1, ' ' * (indent + 4) + '"health_status": "Legacy Mode",\n')
                lines.insert(i + 2, ' ' * (indent + 4) + '"health_score": 50\n')
                lines.insert(i + 3, ' ' * indent + '}\n')
            
            elif 'jsonify({' in line and 'success' in line:
                # Break JSON responses
                indent = len(line) - len(line.lstrip())
                if 'Data received and processed' in line:
                    lines[i] = ' ' * indent + 'jsonify({\n'
                    lines.insert(i + 1, ' ' * (indent + 4) + '"status": "success",\n')
                    lines.insert(i + 2, ' ' * (indent + 4) + '"message": "Data received and processed"\n')
                    lines.insert(i + 3, ' ' * indent + '}),\n')
                elif 'Data received' in line and 'legacy' not in line and 'processing' not in line:
                    lines[i] = ' ' * indent + 'jsonify({\n'
                    lines.insert(i + 1, ' ' * (indent + 4) + '"status": "success",\n')
                    lines.insert(i + 2, ' ' * (indent + 4) + '"message": "Data received"\n')
                    lines.insert(i + 3, ' ' * indent + '}),\n')
    
    # Write back the fixed content
    with open('app_monitor.py', 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print("✅ Applied final cleanup fixes")

if __name__ == "__main__":
    fix_remaining_issues()
