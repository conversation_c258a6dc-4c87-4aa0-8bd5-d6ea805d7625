{"python.defaultInterpreterPath": "python", "python.terminal.activateEnvironment": true, "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.formatting.blackArgs": ["--line-length=100"], "python.sortImports.args": ["--profile=black"], "python.testing.pytestEnabled": true, "python.testing.pytestArgs": ["tests"], "python.testing.unittestEnabled": false, "python.testing.autoTestDiscoverOnSaveEnabled": true, "files.associations": {"*.html": "html", "*.css": "css", "*.js": "javascript", "*.json": "jsonc", "*.md": "markdown", "*.py": "python", "*.yml": "yaml", "*.yaml": "yaml", "Dockerfile": "dockerfile", "Makefile": "makefile", ".env*": "properties"}, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/.pytest_cache": true, "**/node_modules": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/*.egg-info": true, "**/dist": true, "**/build": true}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/*.code-search": true, "**/__pycache__": true, "**/*.pyc": true, "**/venv": true, "**/env": true, "**/.venv": true}, "emmet.includeLanguages": {"jinja-html": "html", "jinja2": "html"}, "html.suggest.html5": true, "css.validate": true, "css.lint.unknownAtRules": "ignore", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "editor.rulers": [88, 100], "editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": true, "terminal.integrated.env.windows": {"PYTHONPATH": "${workspaceFolder}"}, "terminal.integrated.env.linux": {"PYTHONPATH": "${workspaceFolder}"}, "terminal.integrated.env.osx": {"PYTHONPATH": "${workspaceFolder}"}, "workbench.colorCustomizations": {"terminal.background": "#1e1e1e", "terminal.foreground": "#d4d4d4"}, "extensions.recommendations": ["ms-python.python", "ms-python.flake8", "ms-python.black-formatter", "ms-python.isort", "bradlc.vscode-tailwindcss", "formulahendry.auto-rename-tag", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.makefile-tools"]}