{"version": "2.0.0", "tasks": [{"label": "🍎 Start Food Monitor", "type": "shell", "command": "python", "args": ["app_monitor.py"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1", "PYTHONPATH": "${workspaceFolder}"}}, "problemMatcher": [], "runOptions": {"instanceLimit": 1}}, {"label": "🚀 Auto Setup & Start", "type": "shell", "command": "python", "args": ["start_monitor.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, "problemMatcher": []}, {"label": "🧪 Run Tests", "type": "shell", "command": "python", "args": ["test_app_monitor.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": true}, "options": {"cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, "problemMatcher": []}, {"label": "📦 Install Dependencies", "type": "shell", "command": "pip", "args": ["install", "-r", "requirements.txt"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": true}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "🔍 Check Code Quality", "type": "shell", "command": "python", "args": ["-m", "flake8", "app_monitor.py", "start_monitor.py", "test_app_monitor.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": true}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "🎨 Format Code", "type": "shell", "command": "python", "args": ["-m", "black", "app_monitor.py", "start_monitor.py", "test_app_monitor.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": true}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "🗄️ Initialize Database", "type": "shell", "command": "python", "args": ["-c", "import sqlite3; conn = sqlite3.connect('food_monitoring.db'); print('Database created successfully')"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": true}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "🌐 Open in Browser", "type": "shell", "command": "python", "args": ["-c", "import webbrowser; webbrowser.open('http://localhost:5000')"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "📊 Send Test Data", "type": "shell", "command": "curl", "args": ["-X", "POST", "http://localhost:5000/api/data", "-H", "Content-Type: application/json", "-d", "{\"device_id\":\"TEST_SENSOR_001\",\"temperature\":3.5,\"humidity\":85.0}"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": true}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "🔧 Clean Cache", "type": "shell", "command": "python", "args": ["-c", "import shutil, os; [shutil.rmtree(d) for d in ['__pycache__', '.pytest_cache'] if os.path.exists(d)]; print('Cache cleaned')"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}]}