#!/usr/bin/env python3
"""
Reports Manager Backend
Handles data analytics, report generation, and data export
"""

import csv
import io
import json
import logging
import sqlite3
import statistics
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class SensorStatistics:
    """Sensor statistics data class"""

    device_id: str
    location: str
    product_type: str
    total_readings: int
    avg_temperature: float
    min_temperature: float
    max_temperature: float
    avg_humidity: float
    min_humidity: float
    max_humidity: float
    temp_variance: float
    humidity_variance: float
    uptime_percentage: float
    alert_count: int
    last_reading_time: str


@dataclass
class SystemReport:
    """System report data class"""

    report_id: str
    report_type: str
    time_period: str
    generated_at: str
    total_sensors: int
    total_readings: int
    total_alerts: int
    system_uptime: float
    data_quality_score: float
    summary: Dict
    recommendations: List[str]


class ReportsManager:
    """Comprehensive reports and analytics management system"""

    def __init__(self, db_path: str = "food_monitoring.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """Initialize reports database tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Reports metadata table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS reports_metadata (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    report_id TEXT UNIQUE NOT NULL,
                    report_type TEXT NOT NULL,
                    report_name TEXT NOT NULL,
                    description TEXT,
                    time_period TEXT NOT NULL,
                    parameters TEXT,
                    file_path TEXT,
                    file_size INTEGER,
                    generated_by TEXT,
                    generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    expires_at DATETIME
                )
            """
            )

            # Data quality metrics table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS data_quality_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id TEXT NOT NULL,
                    metric_date DATE NOT NULL,
                    total_expected_readings INTEGER NOT NULL,
                    actual_readings INTEGER NOT NULL,
                    missing_readings INTEGER NOT NULL,
                    duplicate_readings INTEGER NOT NULL,
                    out_of_range_readings INTEGER NOT NULL,
                    quality_score REAL NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(device_id, metric_date)
                )
            """
            )

            # Performance benchmarks table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS performance_benchmarks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    benchmark_name TEXT NOT NULL,
                    device_id TEXT,
                    product_type TEXT,
                    metric_name TEXT NOT NULL,
                    target_value REAL NOT NULL,
                    tolerance_range REAL NOT NULL,
                    measurement_unit TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """
            )

            # Create sensor_data table if it doesn't exist
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS sensor_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id TEXT NOT NULL,
                    temperature REAL NOT NULL,
                    humidity REAL NOT NULL,
                    location TEXT,
                    product_type TEXT,
                    batch_id TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (device_id) REFERENCES sensor_configs (device_id)
                )
            """
            )

            conn.commit()

        self._insert_default_benchmarks()
        self._insert_sample_data()
        logger.info("Reports database initialized successfully")

    def _insert_default_benchmarks(self):
        """Insert default performance benchmarks"""
        default_benchmarks = [
            (
                "Temperature Stability",
                None,
                "Fresh Meat",
                "temperature_variance",
                1.0,
                0.5,
                "°C",
                True,
            ),
            (
                "Humidity Stability",
                None,
                "Fresh Meat",
                "humidity_variance",
                2.0,
                1.0,
                "%",
                True,
            ),
            (
                "Data Availability",
                None,
                None,
                "uptime_percentage",
                95.0,
                5.0,
                "%",
                True,
            ),
            ("Alert Frequency", None, None, "alerts_per_day", 2.0, 1.0, "count", True),
        ]

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            for benchmark in default_benchmarks:
                cursor.execute(
                    """
                    INSERT OR IGNORE INTO performance_benchmarks
                    (benchmark_name, device_id, product_type, metric_name, target_value,
                     tolerance_range, measurement_unit, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """,
                    benchmark,
                )

            conn.commit()

    def _insert_sample_data(self):
        """Insert sample sensor data for demonstration"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Check if sample data already exists
                cursor.execute("SELECT COUNT(*) FROM sensor_data")
                count = cursor.fetchone()[0]

                if count == 0:
                    # Insert sample sensor data
                    sample_data = [
                        (
                            "SENSOR_001",
                            3.2,
                            85.5,
                            "Cold Storage Room A",
                            "Fresh Meat",
                            "BATCH_001",
                        ),
                        (
                            "SENSOR_001",
                            3.8,
                            84.2,
                            "Cold Storage Room A",
                            "Fresh Meat",
                            "BATCH_001",
                        ),
                        (
                            "SENSOR_001",
                            2.9,
                            86.1,
                            "Cold Storage Room A",
                            "Fresh Meat",
                            "BATCH_001",
                        ),
                        (
                            "SENSOR_002",
                            2.1,
                            78.3,
                            "Dairy Refrigerator",
                            "Dairy Products",
                            "BATCH_002",
                        ),
                        (
                            "SENSOR_002",
                            2.5,
                            79.1,
                            "Dairy Refrigerator",
                            "Dairy Products",
                            "BATCH_002",
                        ),
                        (
                            "SENSOR_002",
                            1.8,
                            77.8,
                            "Dairy Refrigerator",
                            "Dairy Products",
                            "BATCH_002",
                        ),
                        (
                            "SENSOR_003",
                            5.2,
                            88.5,
                            "Vegetable Storage",
                            "Fresh Vegetables",
                            "BATCH_003",
                        ),
                        (
                            "SENSOR_003",
                            4.8,
                            89.1,
                            "Vegetable Storage",
                            "Fresh Vegetables",
                            "BATCH_003",
                        ),
                    ]

                    for data in sample_data:
                        cursor.execute(
                            """
                            INSERT INTO sensor_data
                            (device_id, temperature, humidity, location, product_type, batch_id)
                            VALUES (?, ?, ?, ?, ?, ?)
                        """,
                            data,
                        )

                    conn.commit()
                    logger.info("Sample sensor data inserted")

        except Exception as e:
            logger.error(f"Error inserting sample data: {e}")

    def store_sensor_data(self, sensor_data: Dict) -> bool:
        """Store sensor data in the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute(
                    """
                    INSERT INTO sensor_data
                    (device_id, temperature, humidity, location, product_type,
                     batch_id, timestamp, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """,
                    (
                        sensor_data["device_id"],
                        sensor_data["temperature"],
                        sensor_data["humidity"],
                        sensor_data.get("location", ""),
                        sensor_data.get("product_type", ""),
                        sensor_data.get("batch_id", ""),
                        sensor_data.get("timestamp", datetime.now().isoformat()),
                        datetime.now().isoformat(),
                    ),
                )

                conn.commit()
                logger.debug(f"Stored sensor data for {sensor_data['device_id']}")
                return True

        except Exception as e:
            logger.error(f"Error storing sensor data: {e}")
            return False

    def get_sensor_data(self, device_id: str, hours: int = 24) -> List[Dict]:
        """Get historical sensor data for a specific device"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                end_time = datetime.now()
                start_time = end_time - timedelta(hours=hours)

                cursor.execute(
                    """
                    SELECT * FROM sensor_data
                    WHERE device_id = ? AND timestamp >= ?
                    ORDER BY timestamp DESC
                """,
                    (device_id, start_time.isoformat()),
                )

                rows = cursor.fetchall()
                return [dict(row) for row in rows]

        except Exception as e:
            logger.error(f"Error getting sensor data for {device_id}: {e}")
            return []

    def get_all_data(self, hours: int = 24) -> List[Dict]:
        """Get all sensor data for the specified time period"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                end_time = datetime.now()
                start_time = end_time - timedelta(hours=hours)

                cursor.execute(
                    """
                    SELECT * FROM sensor_data
                    WHERE timestamp >= ?
                    ORDER BY timestamp DESC
                """,
                    (start_time.isoformat(),),
                )

                rows = cursor.fetchall()
                return [dict(row) for row in rows]

        except Exception as e:
            logger.error(f"Error getting all sensor data: {e}")
            return []

    def export_to_csv(self, hours: int = 24) -> str:
        """Export sensor data to CSV format"""
        try:
            data = self.get_all_data(hours=hours)
            return self._export_to_csv(data)
        except Exception as e:
            logger.error(f"Error exporting to CSV: {e}")
            return ""

    def get_system_overview(self, hours: int = 24) -> Dict:
        """Get comprehensive system overview"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Total sensors
                cursor.execute(
                    "SELECT COUNT(*) FROM sensor_configs WHERE is_active = 1"
                )
                total_sensors = cursor.fetchone()[0]

                # Total readings
                cursor.execute(
                    """
                    SELECT COUNT(*) FROM sensor_data
                    WHERE timestamp >= datetime('now', '-{} hours')
                """.format(
                        hours
                    )
                )
                total_readings = cursor.fetchone()[0]

                # Total alerts
                cursor.execute(
                    """
                    SELECT COUNT(*) FROM alerts
                    WHERE created_at >= datetime('now', '-{} hours')
                """.format(
                        hours
                    )
                )
                total_alerts = cursor.fetchone()[0]

                # Average temperature and humidity
                cursor.execute(
                    """
                    SELECT AVG(temperature), AVG(humidity) FROM sensor_data
                    WHERE timestamp >= datetime('now', '-{} hours')
                """.format(
                        hours
                    )
                )
                avg_temp, avg_humidity = cursor.fetchone()

                # Temperature and humidity ranges
                cursor.execute(
                    """
                    SELECT MIN(temperature), MAX(temperature), MIN(humidity), MAX(humidity)
                    FROM sensor_data
                    WHERE timestamp >= datetime('now', '-{} hours')
                """.format(
                        hours
                    )
                )
                min_temp, max_temp, min_humidity, max_humidity = cursor.fetchone()

                # Alert breakdown by severity
                cursor.execute(
                    """
                    SELECT severity, COUNT(*) FROM alerts
                    WHERE created_at >= datetime('now', '-{} hours')
                    GROUP BY severity
                """.format(
                        hours
                    )
                )
                alert_breakdown = dict(cursor.fetchall())

                return {
                    "total_sensors": total_sensors,
                    "total_readings": total_readings,
                    "total_alerts": total_alerts,
                    "avg_temperature": round(avg_temp, 2) if avg_temp else 0,
                    "avg_humidity": round(avg_humidity, 2) if avg_humidity else 0,
                    "min_temperature": min_temp,
                    "max_temperature": max_temp,
                    "min_humidity": min_humidity,
                    "max_humidity": max_humidity,
                    "alert_breakdown": alert_breakdown,
                    "time_period_hours": hours,
                }

        except Exception as e:
            logger.error(f"Error getting system overview: {e}")
            return {}

    def get_sensor_statistics(
        self, device_id: str = None, hours: int = 24
    ) -> List[SensorStatistics]:
        """Get detailed statistics for sensors"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Get sensor configurations
                if device_id:
                    cursor.execute(
                        "SELECT * FROM sensor_configs WHERE device_id = ?", (device_id,)
                    )
                    sensors = cursor.fetchall()
                else:
                    cursor.execute("SELECT * FROM sensor_configs WHERE is_active = 1")
                    sensors = cursor.fetchall()

                statistics = []

                for sensor in sensors:
                    sensor_id = sensor["device_id"]

                    # Get sensor data for the time period
                    cursor.execute(
                        """
                        SELECT COUNT(*) as total_readings,
                               AVG(temperature) as avg_temp,
                               MIN(temperature) as min_temp,
                               MAX(temperature) as max_temp,
                               AVG(humidity) as avg_humidity,
                               MIN(humidity) as min_humidity,
                               MAX(humidity) as max_humidity,
                               MAX(timestamp) as last_reading
                        FROM sensor_data
                        WHERE device_id = ? AND timestamp >= datetime('now', '-{} hours')
                    """.format(
                            hours
                        ),
                        (sensor_id,),
                    )

                    data_stats = cursor.fetchone()

                    # Get alert count
                    cursor.execute(
                        """
                        SELECT COUNT(*) as alert_count
                        FROM alerts
                        WHERE device_id = ? AND created_at >= datetime('now', '-{} hours')
                    """.format(
                            hours
                        ),
                        (sensor_id,),
                    )

                    alert_count = cursor.fetchone()["alert_count"]

                    # Calculate uptime (simplified - based on data availability)
                    expected_readings = hours * 60 / 5  # Assuming 5-minute intervals
                    actual_readings = data_stats["total_readings"] or 0
                    uptime_percentage = (
                        min(100.0, (actual_readings / expected_readings) * 100)
                        if expected_readings > 0
                        else 0
                    )

                    # Calculate variance
                    cursor.execute(
                        """
                        SELECT temperature, humidity
                        FROM sensor_data
                        WHERE device_id = ? AND timestamp >= datetime('now', '-{} hours')
                    """.format(
                            hours
                        ),
                        (sensor_id,),
                    )

                    readings = cursor.fetchall()
                    temp_variance = 0.0
                    humidity_variance = 0.0

                    if readings and len(readings) > 1:
                        temps = [r["temperature"] for r in readings]
                        humidities = [r["humidity"] for r in readings]

                        temp_mean = sum(temps) / len(temps)
                        humidity_mean = sum(humidities) / len(humidities)

                        temp_variance = sum((t - temp_mean) ** 2 for t in temps) / len(
                            temps
                        )
                        humidity_variance = sum(
                            (h - humidity_mean) ** 2 for h in humidities
                        ) / len(humidities)

                    stat = SensorStatistics(
                        device_id=sensor_id,
                        location=sensor["location"],
                        product_type=sensor["product_type"],
                        total_readings=actual_readings,
                        avg_temperature=round(data_stats["avg_temp"] or 0, 2),
                        min_temperature=round(data_stats["min_temp"] or 0, 2),
                        max_temperature=round(data_stats["max_temp"] or 0, 2),
                        avg_humidity=round(data_stats["avg_humidity"] or 0, 2),
                        min_humidity=round(data_stats["min_humidity"] or 0, 2),
                        max_humidity=round(data_stats["max_humidity"] or 0, 2),
                        temp_variance=round(temp_variance, 2),
                        humidity_variance=round(humidity_variance, 2),
                        uptime_percentage=round(uptime_percentage, 2),
                        alert_count=alert_count,
                        last_reading_time=data_stats["last_reading"] or "No data",
                    )

                    statistics.append(stat)

                return statistics

        except Exception as e:
            logger.error(f"Error getting sensor statistics: {e}")
            return []

    def export_sensor_data(
        self, device_id: str = None, hours: int = 24, format: str = "csv"
    ) -> str:
        """Export sensor data to CSV or JSON format"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                if device_id:
                    cursor.execute(
                        """
                        SELECT sd.*, sc.location, sc.product_type
                        FROM sensor_data sd
                        LEFT JOIN sensor_configs sc ON sd.device_id = sc.device_id
                        WHERE sd.device_id = ? AND sd.timestamp >= datetime('now', '-{} hours')
                        ORDER BY sd.timestamp DESC
                    """.format(
                            hours
                        ),
                        (device_id,),
                    )
                else:
                    cursor.execute(
                        """
                        SELECT sd.*, sc.location, sc.product_type
                        FROM sensor_data sd
                        LEFT JOIN sensor_configs sc ON sd.device_id = sc.device_id
                        WHERE sd.timestamp >= datetime('now', '-{} hours')
                        ORDER BY sd.timestamp DESC
                    """.format(
                            hours
                        )
                    )

                data = [dict(row) for row in cursor.fetchall()]

                if format.lower() == "csv":
                    return self._export_to_csv(data)
                elif format.lower() == "json":
                    return self._export_to_json(data)
                else:
                    raise ValueError(f"Unsupported format: {format}")

        except Exception as e:
            logger.error(f"Error exporting sensor data: {e}")
            return ""

    def _export_to_csv(self, data: List[Dict]) -> str:
        """Export data to CSV format"""
        if not data:
            return ""

        output = io.StringIO()
        fieldnames = data[0].keys()
        writer = csv.DictWriter(output, fieldnames=fieldnames)

        writer.writeheader()
        writer.writerows(data)

        csv_content = output.getvalue()
        output.close()

        return csv_content

    def _export_to_json(self, data: List[Dict]) -> str:
        """Export data to JSON format"""
        return json.dumps(
            {
                "export_timestamp": datetime.now().isoformat(),
                "total_records": len(data),
                "data": data,
            },
            indent=2,
        )

    def generate_performance_report(
        self, device_id: str = None, hours: int = 24
    ) -> Dict:
        """Generate comprehensive performance report"""
        try:
            report_id = f"perf_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Get system overview
            overview = self.get_system_overview(hours)

            # Get sensor statistics
            sensor_stats = self.get_sensor_statistics(device_id, hours)

            # Calculate data quality scores
            quality_scores = self._calculate_data_quality_scores(device_id, hours)

            # Generate recommendations
            recommendations = self._generate_recommendations(sensor_stats, overview)

            # Calculate overall system health
            system_health = self._calculate_system_health(sensor_stats, overview)

            report = {
                "report_id": report_id,
                "report_type": "performance",
                "generated_at": datetime.now().isoformat(),
                "time_period_hours": hours,
                "device_id": device_id,
                "overview": overview,
                "sensor_statistics": [asdict(stat) for stat in sensor_stats],
                "data_quality_scores": quality_scores,
                "system_health": system_health,
                "recommendations": recommendations,
                "summary": {
                    "total_sensors_analyzed": len(sensor_stats),
                    "average_uptime": (
                        round(
                            sum(s.uptime_percentage for s in sensor_stats)
                            / len(sensor_stats),
                            2,
                        )
                        if sensor_stats
                        else 0
                    ),
                    "total_alerts": sum(s.alert_count for s in sensor_stats),
                    "data_quality_average": (
                        round(sum(quality_scores.values()) / len(quality_scores), 2)
                        if quality_scores
                        else 0
                    ),
                },
            }

            # Save report metadata
            self._save_report_metadata(report)

            logger.info(f"Generated performance report: {report_id}")
            return report

        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {}

    def _calculate_data_quality_scores(
        self, device_id: str = None, hours: int = 24
    ) -> Dict[str, float]:
        """Calculate data quality scores for sensors"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                if device_id:
                    cursor.execute(
                        "SELECT device_id FROM sensor_configs WHERE device_id = ?",
                        (device_id,),
                    )
                    sensors = [row[0] for row in cursor.fetchall()]
                else:
                    cursor.execute(
                        "SELECT device_id FROM sensor_configs WHERE is_active = 1"
                    )
                    sensors = [row[0] for row in cursor.fetchall()]

                quality_scores = {}

                for sensor_id in sensors:
                    # Expected readings (assuming every 30 minutes)
                    expected_readings = hours * 2

                    # Actual readings
                    cursor.execute(
                        """
                        SELECT COUNT(*) FROM sensor_data
                        WHERE device_id = ? AND timestamp >= datetime('now', '-{} hours')
                    """.format(
                            hours
                        ),
                        (sensor_id,),
                    )
                    actual_readings = cursor.fetchone()[0]

                    # Out of range readings (basic check)
                    cursor.execute(
                        """
                        SELECT COUNT(*) FROM sensor_data
                        WHERE device_id = ? AND timestamp >= datetime('now', '-{} hours')
                        AND (temperature < -50 OR temperature > 100 OR humidity < 0 OR humidity > 100)
                    """.format(
                            hours
                        ),
                        (sensor_id,),
                    )
                    out_of_range = cursor.fetchone()[0]

                    # Calculate quality score
                    completeness_score = (
                        min(100, (actual_readings / expected_readings) * 100)
                        if expected_readings > 0
                        else 0
                    )
                    accuracy_score = max(
                        0, 100 - (out_of_range / max(actual_readings, 1)) * 100
                    )

                    overall_score = completeness_score * 0.7 + accuracy_score * 0.3
                    quality_scores[sensor_id] = round(overall_score, 2)

                return quality_scores

        except Exception as e:
            logger.error(f"Error calculating data quality scores: {e}")
            return {}

    def _generate_recommendations(
        self, sensor_stats: List[SensorStatistics], overview: Dict
    ) -> List[str]:
        """Generate actionable recommendations based on data analysis"""
        recommendations = []

        try:
            # Check for low uptime sensors
            low_uptime_sensors = [s for s in sensor_stats if s.uptime_percentage < 90]
            if low_uptime_sensors:
                recommendations.append(
                    f"⚠️ {len(low_uptime_sensors)} sensor(s) have low uptime (<90%). "
                    f"Check connectivity and power supply for: {', '.join([s.device_id for s in low_uptime_sensors])}"
                )

            # Check for high alert frequency
            high_alert_sensors = [s for s in sensor_stats if s.alert_count > 5]
            if high_alert_sensors:
                recommendations.append(
                    f"🚨 {len(high_alert_sensors)} sensor(s) have high alert frequency (>5 alerts). "
                    f"Review thresholds and environmental conditions for: {', '.join([s.device_id for s in high_alert_sensors])}"
                )

            # Check for high temperature variance
            unstable_temp_sensors = [s for s in sensor_stats if s.temp_variance > 2.0]
            if unstable_temp_sensors:
                recommendations.append(
                    f"🌡️ {len(unstable_temp_sensors)} sensor(s) show high temperature variance (>2°C). "
                    f"Check cooling system stability for: {', '.join([s.device_id for s in unstable_temp_sensors])}"
                )

            # Check for high humidity variance
            unstable_humidity_sensors = [
                s for s in sensor_stats if s.humidity_variance > 5.0
            ]
            if unstable_humidity_sensors:
                recommendations.append(
                    f"💧 {len(unstable_humidity_sensors)} sensor(s) show high humidity variance (>5%). "
                    f"Check ventilation and humidity control for: {', '.join([s.device_id for s in unstable_humidity_sensors])}"
                )

            # Overall system recommendations
            if overview.get("total_alerts", 0) > 20:
                recommendations.append(
                    "📊 High overall alert count detected. Consider reviewing system-wide settings and maintenance schedules."
                )

            if not recommendations:
                recommendations.append(
                    "✅ All systems are operating within normal parameters. Continue regular monitoring."
                )

            return recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return ["❌ Unable to generate recommendations due to data analysis error."]

    def _calculate_system_health(
        self, sensor_stats: List[SensorStatistics], overview: Dict
    ) -> Dict:
        """Calculate overall system health metrics"""
        try:
            if not sensor_stats:
                return {"overall_score": 0, "status": "No Data", "components": {}}

            # Component scores
            uptime_score = sum(s.uptime_percentage for s in sensor_stats) / len(
                sensor_stats
            )
            alert_score = max(
                0, 100 - (overview.get("total_alerts", 0) * 2)
            )  # Penalize alerts
            stability_score = 100 - sum(
                min(s.temp_variance * 10, 50) for s in sensor_stats
            ) / len(sensor_stats)

            # Overall health score
            overall_score = (
                uptime_score * 0.4 + alert_score * 0.3 + stability_score * 0.3
            )

            # Determine status
            if overall_score >= 90:
                status = "Excellent"
            elif overall_score >= 75:
                status = "Good"
            elif overall_score >= 60:
                status = "Fair"
            else:
                status = "Poor"

            return {
                "overall_score": round(overall_score, 2),
                "status": status,
                "components": {
                    "uptime": round(uptime_score, 2),
                    "alerts": round(alert_score, 2),
                    "stability": round(stability_score, 2),
                },
            }

        except Exception as e:
            logger.error(f"Error calculating system health: {e}")
            return {"overall_score": 0, "status": "Error", "components": {}}

    def _save_report_metadata(self, report: Dict):
        """Save report metadata to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    INSERT INTO reports_metadata
                    (report_id, report_type, report_name, description, time_period,
                     parameters, generated_by, expires_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """,
                    (
                        report["report_id"],
                        report["report_type"],
                        f"Performance Report - {report['generated_at']}",
                        f"System performance analysis for {report['time_period_hours']} hours",
                        f"{report['time_period_hours']} hours",
                        json.dumps({"device_id": report.get("device_id")}),
                        "system",
                        (
                            datetime.now() + timedelta(days=30)
                        ).isoformat(),  # Expire in 30 days
                    ),
                )
                conn.commit()

        except Exception as e:
            logger.error(f"Error saving report metadata: {e}")

    def get_trend_analysis(
        self, device_id: str, metric: str = "temperature", days: int = 7
    ) -> Dict:
        """Analyze trends for a specific metric over time"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Get daily averages
                cursor.execute(
                    f"""
                    SELECT DATE(timestamp) as date, AVG({metric}) as avg_value
                    FROM sensor_data
                    WHERE device_id = ? AND timestamp >= datetime('now', '-{days} days')
                    GROUP BY DATE(timestamp)
                    ORDER BY date
                """,
                    (device_id,),
                )

                daily_data = cursor.fetchall()

                if len(daily_data) < 2:
                    return {"trend": "insufficient_data", "slope": 0, "correlation": 0}

                # Calculate trend
                dates = list(range(len(daily_data)))
                values = [row[1] for row in daily_data]

                # Simple linear regression
                n = len(dates)
                sum_x = sum(dates)
                sum_y = sum(values)
                sum_xy = sum(x * y for x, y in zip(dates, values))
                sum_x2 = sum(x * x for x in dates)

                slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)

                # Determine trend direction
                if abs(slope) < 0.1:
                    trend = "stable"
                elif slope > 0:
                    trend = "increasing"
                else:
                    trend = "decreasing"

                return {
                    "trend": trend,
                    "slope": round(slope, 4),
                    "daily_averages": [
                        {"date": row[0], "value": round(row[1], 2)}
                        for row in daily_data
                    ],
                    "analysis_period_days": days,
                }

        except Exception as e:
            logger.error(f"Error analyzing trends: {e}")
            return {"trend": "error", "slope": 0, "correlation": 0}
