#!/usr/bin/env python3
"""
Food Monitoring System - Enhanced Application v2.0
Advanced frontend-backend integration with comprehensive database system
Features:
- Dual storage system (Backend + Database)
- Real-time monitoring and alerts
- Advanced analytics and reporting
- Robust error handling and recovery
- Performance optimization
"""

import json
import logging
import os
import sqlite3
import threading
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from flask import Flask, flash, jsonify, redirect, render_template, request, url_for

# Configure enhanced logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("monitoring_app.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Initialize Flask app with enhanced configuration
app = Flask(__name__)
app.secret_key = os.environ.get(
    "SECRET_KEY", "food-monitoring-secret-key-v2-change-this"
)
app.config.update(
    {
        "MAX_CONTENT_LENGTH": 16 * 1024 * 1024,  # 16MB max file upload
        "PERMANENT_SESSION_LIFETIME": timedelta(hours=24),
        "JSON_SORT_KEYS": False,
        "JSONIFY_PRETTYPRINT_REGULAR": True,
    }
)

# Initialize backend system
backend = None
try:
    from backend import BackendManager

    backend = BackendManager("food_monitoring.db")
    print("✅ Backend system initialized successfully")
    print("   • Settings Manager - System and sensor configuration")
    print("   • Alerts Manager - Real-time alerting and notifications")
    print("   • Reports Manager - Analytics and data export")
except Exception as e:
    print(f"❌ Error initializing backend: {e}")

# Initialize database system
db = None
try:
    from database import DatabaseManager

    db = DatabaseManager("monitoring_app/monitoring.db")
    print("✅ Database system initialized successfully")
    print("   • SQLite database with optimized performance")
    print("   • Sensor data storage and retrieval")
    print("   • Alert logging and history")
    print("   • System statistics and reporting")
except Exception as e:
    print(f"❌ Error initializing database: {e}")
    # Try alternative database path
    try:
        db = DatabaseManager("monitoring.db")
        print("✅ Database system initialized (alternative path)")
    except Exception as e2:
        print(f"❌ Database fallback also failed: {e2}")

# Fallback components for compatibility
monitoring_core = None
alert_system = None

try:
    from alert_system import AlertSystem
    from monitoring_core import MonitoringCore

    db = DatabaseManager()
    monitoring_core = MonitoringCore()
    alert_system = AlertSystem(monitoring_core.config)
    print("✅ Legacy components initialized successfully")
except Exception as e:
    print(f"⚠️ Legacy components not available: {e}")

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================


def get_safe_data(func, default=None):
    """Safely execute a function and return default on error"""
    try:
        return func()
    except Exception as e:
        logger.error(f"Error in {func.__name__}: {e}")
        return default


def init_database_tables():
    """Initialize database tables if they don't exist"""
    if not db:
        return False

    try:
        # This will create tables if they don't exist
        db.init_database()
        logger.info("Database tables initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Database table initialization error: {e}")
        return False


def get_database_status():
    """Get current database connection status"""
    status = {
        "backend_connected": backend is not None,
        "database_connected": db is not None,
        "tables_initialized": False,
        "last_check": datetime.now().isoformat(),
    }

    if db:
        try:
            # Test database connection
            test_data = db.get_sensor_config()
            status["tables_initialized"] = True
            status["sensor_count"] = len(test_data) if test_data else 0
        except Exception as e:
            logger.error(f"Database status check error: {e}")
            status["error"] = str(e)

    return status


def ensure_database_connection():
    """Ensure database connection is available, try to reconnect if needed"""
    global db

    if db:
        return True

    try:
        from database import DatabaseManager

        db = DatabaseManager("monitoring_app/monitoring.db")
        init_database_tables()
        logger.info("Database connection restored")
        return True
    except Exception as e:
        logger.error(f"Database reconnection failed: {e}")
        return False


def ensure_dict(data, keys_with_defaults):
    """Ensure data is a dict with required keys"""
    if not isinstance(data, dict):
        data = {}

    for key, default_value in keys_with_defaults.items():
        if key not in data:
            data[key] = default_value

    return data


# ============================================================================
# DASHBOARD ROUTES
# ============================================================================


@app.route("/")
def dashboard():
    """Enhanced dashboard with proper error handling"""
    try:
        # Initialize default data structure
        overview = {
            "total_sensors": 0,
            "total_readings": 0,
            "total_alerts": 0,
            "avg_temperature": 0.0,
            "avg_humidity": 0.0,
            "monitoring_active": False,
            "active_alerts": 0,
        }

        # Status is same as overview for compatibility
        status = overview.copy()
        # Stats is same as overview for compatibility
        stats = overview.copy()

        active_alerts = []
        sensor_stats = []
        system_health = {"health_status": "Unknown", "health_score": 0}

        if backend:
            # Get data from backend with error handling
            try:
                dashboard_data = get_safe_data(lambda: backend.get_dashboard_data(), {})
                if dashboard_data and "overview" in dashboard_data:
                    overview.update(dashboard_data["overview"])
                    status.update(dashboard_data["overview"])
                    stats.update(dashboard_data["overview"])
            except Exception as e:
                logger.error(f"Error getting dashboard data: {e}")

            try:
                system_health = get_safe_data(
                    lambda: backend.get_system_health(), system_health
                )
            except Exception as e:
                logger.error(f"Error getting system health: {e}")

            try:
                active_alerts = get_safe_data(
                    lambda: backend.alerts.get_alerts(acknowledged=False, limit=10), []
                )
                # Update active alerts count
                overview["active_alerts"] = len(active_alerts)
                status["active_alerts"] = len(active_alerts)
                stats["active_alerts"] = len(active_alerts)
            except Exception as e:
                logger.error(f"Error getting alerts: {e}")

            try:
                sensor_stats = get_safe_data(
                    lambda: backend.reports.get_sensor_statistics(hours=24), []
                )
                # Update sensor count
                overview["total_sensors"] = len(sensor_stats)
                status["total_sensors"] = len(sensor_stats)
                stats["total_sensors"] = len(sensor_stats)
            except Exception as e:
                logger.error(f"Error getting sensor statistics: {e}")

            # Set monitoring as active if backend is working
            overview["monitoring_active"] = True
            status["monitoring_active"] = True
            stats["monitoring_active"] = True

        elif db and monitoring_core:
            # Fallback to legacy system
            try:
                legacy_status = get_safe_data(
                    lambda: monitoring_core.get_system_status(), {}
                )
                if legacy_status:
                    overview.update(legacy_status)
                    status.update(legacy_status)
                    stats.update(legacy_status)

                active_alerts = get_safe_data(lambda: db.get_active_alerts(), [])
                legacy_stats = get_safe_data(lambda: db.get_statistics(hours=24), {})
                if legacy_stats:
                    stats.update(legacy_stats)

                system_health = {"health_status": "Legacy Mode", "health_score": 50}

                # Update counts
                overview["active_alerts"] = len(active_alerts)
                status["active_alerts"] = len(active_alerts)
                stats["active_alerts"] = len(active_alerts)

            except Exception as e:
                logger.error(f"Error with legacy system: {e}")
        else:
            # Demo fallback data
            overview.update(
                {
                    "total_sensors": 5,
                    "total_readings": 1250,
                    "total_alerts": 3,
                    "avg_temperature": 3.2,
                    "avg_humidity": 85.5,
                    "monitoring_active": False,
                    "active_alerts": 3,
                }
            )
            status.update(overview)
            stats.update(overview)

            active_alerts = [
                {
                    "id": 1,
                    "device_id": "SENSOR_001",
                    "alert_type": "TEMPERATURE_HIGH",
                    "severity": "HIGH",
                    "message": "Temperature exceeded safe limits",
                    "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "current_value": 6.5,
                    "threshold_value": 4.0,
                }
            ]

            system_health = {"health_status": "Demo Mode", "health_score": 75}

        # Ensure all required template variables are present
        template_vars = {
            "overview": overview,
            "status": status,
            "stats": stats,
            "alerts": active_alerts,
            "sensor_statistics": sensor_stats,
            "system_health": system_health,
            "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "active_alerts_count": len(active_alerts),
        }

        return render_template("dashboard.html", **template_vars)

    except Exception as e:
        logger.error(f"Dashboard error: {e}")
        flash(f"Dashboard error: {e}", "error")

        # Return safe fallback data
        safe_data = {
            "overview": {
                "total_sensors": 0,
                "total_readings": 0,
                "total_alerts": 0,
                "avg_temperature": 0,
                "avg_humidity": 0,
                "monitoring_active": False,
                "active_alerts": 0,
            },
            "status": {
                "total_sensors": 0,
                "total_readings": 0,
                "total_alerts": 0,
                "avg_temperature": 0,
                "avg_humidity": 0,
                "monitoring_active": False,
                "active_alerts": 0,
            },
            "stats": {
                "total_sensors": 0,
                "total_readings": 0,
                "total_alerts": 0,
                "avg_temperature": 0,
                "avg_humidity": 0,
                "monitoring_active": False,
                "active_alerts": 0,
            },
            "alerts": [],
            "sensor_statistics": [],
            "system_health": {"health_status": "Error", "health_score": 0},
            "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "active_alerts_count": 0,
        }

        return render_template("dashboard.html", **safe_data)


@app.route("/api/dashboard/data")
def dashboard_api():
    """API endpoint for dashboard data"""
    try:
        if backend:
            data = get_safe_data(lambda: backend.get_dashboard_data(), {})
            return jsonify(data)
        else:
            return jsonify({"error": "Backend not available"}), 503
    except Exception as e:
        logger.error(f"Dashboard API error: {e}")
        return jsonify({"error": str(e)}), 500


# ============================================================================
# ALERTS ROUTES
# ============================================================================


@app.route("/alerts")
def alerts_page():
    """Enhanced alerts page with backend data"""
    try:
        active_alerts = []
        alert_stats = {}

        if backend:
            try:
                active_alerts = get_safe_data(
                    lambda: backend.alerts.get_alerts(acknowledged=False, limit=100), []
                )
                alert_stats = get_safe_data(
                    lambda: backend.alerts.get_alert_statistics(hours=24), {}
                )
            except Exception as e:
                logger.error(f"Error getting alerts: {e}")
        else:
            # Demo fallback data
            active_alerts = [
                {
                    "id": 1,
                    "device_id": "SENSOR_001",
                    "alert_type": "TEMPERATURE_HIGH",
                    "severity": "HIGH",
                    "message": "Temperature exceeded safe limits",
                    "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "current_value": 6.5,
                    "threshold_value": 4.0,
                }
            ]
            alert_stats = {
                "total_alerts_24h": 1,
                "critical_alerts": 0,
                "high_alerts": 1,
            }

        return render_template(
            "alerts.html", alerts=active_alerts, alert_statistics=alert_stats
        )

    except Exception as e:
        logger.error(f"Alerts page error: {e}")
        return render_template("alerts.html", alerts=[], alert_statistics={})


# ============================================================================
# REPORTS ROUTES
# ============================================================================


@app.route("/reports")
def reports():
    """Enhanced reports page with comprehensive backend integration"""
    try:
        # Get reports data from backend
        reports_data = {}
        sensor_statistics = []
        backend_status = "disconnected"
        export_formats = ["CSV", "JSON", "PDF"]

        if backend:
            try:
                # Get comprehensive reports data
                reports_data = get_safe_data(
                    lambda: backend.reports.get_system_overview(hours=24), {}
                )
                sensor_statistics = get_safe_data(
                    lambda: backend.reports.get_sensor_statistics(hours=24), []
                )

                # Get additional analytics
                alert_summary = get_safe_data(
                    lambda: backend.alerts.get_alert_statistics(hours=24), {}
                )
                reports_data.update(alert_summary)

                backend_status = "connected"
                logger.info(f"Reports loaded: {len(sensor_statistics)} sensor stats")

            except Exception as e:
                logger.error(f"Backend error in reports: {e}")
                backend_status = "error"
        elif db:
            try:
                # Fallback to legacy database
                sensor_statistics = get_safe_data(
                    lambda: db.get_statistics(hours=24), []
                )
                backend_status = "legacy"
                logger.info(f"Reports loaded from legacy database")
            except Exception as e:
                logger.error(f"Legacy database error: {e}")
                backend_status = "error"
        else:
            backend_status = "demo"
            # Demo data for reports
            reports_data = {
                "total_sensors": 3,
                "active_alerts": 1,
                "data_points_24h": 1440,
                "system_uptime": "99.5%",
            }
            sensor_statistics = [
                {
                    "device_id": "DEMO_001",
                    "location": "Cold Storage A",
                    "avg_temperature": 2.5,
                    "avg_humidity": 85.0,
                    "readings_count": 480,
                }
            ]

        return render_template(
            "reports.html",
            reports_data=reports_data,
            sensor_statistics=sensor_statistics,
            backend_status=backend_status,
            export_formats=export_formats,
        )
    except Exception as e:
        logger.error(f"Reports page error: {e}")
        return render_template(
            "reports.html",
            reports_data={},
            sensor_statistics=[],
            backend_status="error",
            export_formats=[],
        )


@app.route("/sensors")
def sensors():
    """Enhanced sensors page with backend integration"""
    try:
        sensors = []
        backend_status = "disconnected"

        if backend:
            try:
                sensors = get_safe_data(
                    lambda: backend.settings.get_sensor_config(), []
                )
                backend_status = "connected"
                logger.info(f"Sensors page loaded: {len(sensors)} sensors")
            except Exception as e:
                logger.error(f"Backend error in sensors: {e}")
                backend_status = "error"
        elif db:
            try:
                sensors = get_safe_data(lambda: db.get_sensor_config(), [])
                backend_status = "legacy"
                logger.info(
                    f"Sensors loaded from legacy database: {len(sensors)} sensors"
                )
            except Exception as e:
                logger.error(f"Legacy database error: {e}")
                backend_status = "error"
        else:
            backend_status = "demo"
            # Demo sensors data
            sensors = [
                {
                    "device_id": "DEMO_001",
                    "location": "Cold Storage A",
                    "product_type": "Fresh Meat",
                    "temp_min": -2,
                    "temp_max": 4,
                    "humidity_min": 80,
                    "humidity_max": 95,
                    "is_active": True,
                }
            ]

        return render_template(
            "sensors.html", sensors=sensors, backend_status=backend_status
        )
    except Exception as e:
        logger.error(f"Sensors page error: {e}")
        return render_template("sensors.html", sensors=[], backend_status="error")


# ============================================================================
# SENSOR DATA API
# ============================================================================


@app.route("/api/data", methods=["POST"])
def receive_sensor_data():
    """Enhanced API endpoint to receive sensor data"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Validate required fields
        required_fields = ["device_id", "temperature", "humidity"]
        if not all(field in data for field in required_fields):
            return jsonify({"error": "Missing required fields"}), 400

        # Prepare sensor data record
        sensor_data_record = {
            "device_id": data["device_id"],
            "temperature": data["temperature"],
            "humidity": data["humidity"],
            "location": data.get("location", ""),
            "product_type": data.get("product_type", ""),
            "batch_id": data.get("batch_id", ""),
            "timestamp": data.get("timestamp", datetime.now().isoformat()),
        }

        # Store data in both backend and database systems
        backend_stored = False
        database_stored = False

        if backend:
            # Process with backend
            try:
                # Store data using reports manager
                backend_stored = backend.reports.store_sensor_data(sensor_data_record)
                if not backend_stored:
                    logger.warning(
                        f"Failed to store sensor data in backend for {data['device_id']}"
                    )
            except Exception as e:
                logger.error(f"Backend storage error for {data['device_id']}: {e}")

        # Also store in database system for redundancy
        if db:
            try:
                database_stored = db.insert_sensor_data(sensor_data_record)
                if not database_stored:
                    logger.warning(
                        f"Failed to store sensor data in database for {data['device_id']}"
                    )
            except Exception as e:
                logger.error(f"Database storage error for {data['device_id']}: {e}")

        # Ensure at least one storage method succeeded
        if not (backend_stored or database_stored):
            logger.error(
                f"Failed to store sensor data anywhere for {data['device_id']}"
            )

        if backend:
            try:

                # Get sensor configuration for threshold checking
                sensor_config = backend.settings.get_sensor_config(data["device_id"])
                if sensor_config:
                    temp = data["temperature"]
                    humidity = data["humidity"]

                    # Check temperature thresholds
                    if temp < sensor_config["temp_min"]:
                        backend.create_sensor_alert(
                            device_id=data["device_id"],
                            alert_type="TEMPERATURE_LOW",
                            severity="HIGH",
                            message=(
                                f"Temperature {temp} degrees C is below "
                                f"minimum {sensor_config['temp_min']}C"
                            ),
                            current_value=temp,
                            threshold_value=sensor_config["temp_min"],
                        )
                    elif temp > sensor_config["temp_max"]:
                        backend.create_sensor_alert(
                            device_id=data["device_id"],
                            alert_type="TEMPERATURE_HIGH",
                            severity="HIGH",
                            message=(
                                f"Temperature {temp}C is above maximum "
                                f"{sensor_config['temp_max']}C"
                            ),
                            current_value=temp,
                            threshold_value=sensor_config["temp_max"],
                        )

                    # Check humidity thresholds
                    if humidity < sensor_config["humidity_min"]:
                        backend.create_sensor_alert(
                            device_id=data["device_id"],
                            alert_type="HUMIDITY_LOW",
                            severity="MEDIUM",
                            message=(
                                f"Humidity {humidity}% is below minimum "
                                f"{sensor_config['humidity_min']}%"
                            ),
                            current_value=humidity,
                            threshold_value=sensor_config["humidity_min"],
                        )
                    elif humidity > sensor_config["humidity_max"]:
                        backend.create_sensor_alert(
                            device_id=data["device_id"],
                            alert_type="HUMIDITY_HIGH",
                            severity="MEDIUM",
                            message=(
                                f"Humidity {humidity}% is above maximum "
                                f"{sensor_config['humidity_max']}%"
                            ),
                            current_value=humidity,
                            threshold_value=sensor_config["humidity_max"],
                        )
                else:
                    logger.warning(f"No sensor config found for {data['device_id']}")

                return (
                    jsonify(
                        {"status": "success", "message": "Data received and processed"}
                    ),
                    200,
                )

            except Exception as e:
                logger.error(f"Backend processing error: {e}")
                return (
                    jsonify(
                        {
                            "status": "success",
                            "message": "Data received (processing error)",
                        }
                    ),
                    200,
                )

        elif monitoring_core:
            # Fallback to legacy processing
            try:
                success = monitoring_core.process_sensor_data(data)
                if success:
                    return (
                        jsonify({"status": "success", "message": "Data received"}),
                        200,
                    )
                else:
                    return jsonify({"error": "Failed to process data"}), 500
            except Exception as e:
                logger.error(f"Legacy processing error: {e}")
                return (
                    jsonify(
                        {"status": "success", "message": "Data received (legacy error)"}
                    ),
                    200,
                )
        else:
            # No processing system available, just acknowledge
            return (
                jsonify(
                    {"status": "success", "message": "Data received (no processing)"}
                ),
                200,
            )

    except Exception as e:
        logger.error(f"Sensor data error: {e}")
        return jsonify({"error": str(e)}), 500


# ============================================================================
# SYSTEM HEALTH AND STATUS
# ============================================================================


@app.route("/api/system/health")
def system_health_api():
    """Get system health status"""
    try:
        if backend:
            health = get_safe_data(
                lambda: backend.get_system_health(),
                {"health_status": "Unknown", "health_score": 0},
            )
            return jsonify(health)
        else:
            return jsonify({"health_status": "Legacy Mode", "health_score": 50})
    except Exception as e:
        logger.error(f"System health error: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/system/status")
def system_status():
    """Get comprehensive system status including database"""
    try:
        # Get database status
        db_status = get_database_status()

        # Get backend status
        backend_status = {}
        if backend:
            backend_status = get_safe_data(
                lambda: backend.get_system_health(), {"health_status": "Unknown"}
            )
        elif monitoring_core:
            backend_status = get_safe_data(
                lambda: monitoring_core.get_system_status(), {"status": "Unknown"}
            )

        # Combine all status information
        system_status_data = {
            "timestamp": datetime.now().isoformat(),
            "database": db_status,
            "backend": backend_status,
            "overall_health": (
                "healthy"
                if (
                    db_status.get("database_connected")
                    and (backend is not None or monitoring_core is not None)
                )
                else "degraded"
            ),
        }

        return jsonify(system_status_data)
    except Exception as e:
        logger.error(f"System status error: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/database/status")
def database_status():
    """Get detailed database status and statistics"""
    try:
        if not ensure_database_connection():
            return jsonify({"error": "Database not available"}), 503

        status = get_database_status()

        # Add additional database statistics
        if db:
            try:
                # Get table counts
                sensor_count = len(get_safe_data(lambda: db.get_sensor_config(), []))
                recent_data = get_safe_data(
                    lambda: db.get_recent_sensor_data(hours=1), []
                )
                alert_count = len(get_safe_data(lambda: db.get_active_alerts(), []))

                status.update(
                    {
                        "sensor_configurations": sensor_count,
                        "recent_data_points": len(recent_data),
                        "active_alerts": alert_count,
                        "database_file": getattr(db, "db_path", "unknown"),
                    }
                )
            except Exception as e:
                status["statistics_error"] = str(e)

        return jsonify(status)
    except Exception as e:
        logger.error(f"Database status error: {e}")
        return jsonify({"error": str(e)}), 500


# ============================================================================
# SETTINGS ROUTES WITH BACKEND INTEGRATION
# ============================================================================


@app.route("/settings")
def settings():
    """Enhanced settings page with comprehensive backend integration"""
    try:
        settings = {}
        sensors = []
        backend_status = "disconnected"

        # Default product types for quick setup
        product_types = [
            {
                "name": "Fresh Meat",
                "description": "Fresh meat products",
                "temp_min": -2,
                "temp_max": 4,
                "humidity_min": 80,
                "humidity_max": 95,
                "shelf_life_hours": 72,
            },
            {
                "name": "Dairy Products",
                "description": "Milk, cheese, yogurt",
                "temp_min": 1,
                "temp_max": 4,
                "humidity_min": 75,
                "humidity_max": 85,
                "shelf_life_hours": 168,
            },
            {
                "name": "Fresh Vegetables",
                "description": "Fresh vegetables and greens",
                "temp_min": 0,
                "temp_max": 8,
                "humidity_min": 85,
                "humidity_max": 95,
                "shelf_life_hours": 120,
            },
            {
                "name": "Frozen Foods",
                "description": "Frozen food products",
                "temp_min": -25,
                "temp_max": -18,
                "humidity_min": 70,
                "humidity_max": 90,
                "shelf_life_hours": 8760,
            },
            {
                "name": "Fruits",
                "description": "Fresh fruits",
                "temp_min": 2,
                "temp_max": 10,
                "humidity_min": 80,
                "humidity_max": 90,
                "shelf_life_hours": 240,
            },
        ]

        if backend:
            try:
                # Get all settings from backend
                settings = get_safe_data(
                    lambda: backend.settings.get_all_settings(), {}
                )

                # Get sensor configurations
                sensors = get_safe_data(
                    lambda: backend.settings.get_sensor_config(), []
                )

                backend_status = "connected"
                logger.info(
                    f"Settings loaded successfully: {len(sensors)} sensors configured"
                )

            except Exception as e:
                logger.error(f"Backend error in settings: {e}")
                backend_status = "error"
        elif db:
            try:
                # Fallback to legacy database
                sensors = get_safe_data(lambda: db.get_sensor_config(), [])
                backend_status = "legacy"
                logger.info(
                    f"Settings loaded from legacy database: {len(sensors)} sensors"
                )
            except Exception as e:
                logger.error(f"Legacy database error: {e}")
                backend_status = "error"
        else:
            backend_status = "demo"

        return render_template(
            "settings.html",
            settings=settings,
            sensors=sensors,
            product_types=product_types,
            backend_status=backend_status,
        )

    except Exception as e:
        logger.error(f"Settings page error: {e}")
        return render_template(
            "settings.html",
            settings={},
            sensors=[],
            product_types=[],
            backend_status="error",
        )


@app.route("/settings/sensor/add", methods=["POST"])
def add_sensor_config():
    """Add new sensor configuration with comprehensive validation"""
    try:
        # Validate required fields
        required_fields = [
            "device_id",
            "location",
            "temp_min",
            "temp_max",
            "humidity_min",
            "humidity_max",
        ]
        missing_fields = [
            field for field in required_fields if not request.form.get(field)
        ]

        if missing_fields:
            return (
                jsonify(
                    {"error": f"Missing required fields: {', '.join(missing_fields)}"}
                ),
                400,
            )

        # Validate numeric ranges
        try:
            temp_min = float(request.form["temp_min"])
            temp_max = float(request.form["temp_max"])
            humidity_min = float(request.form["humidity_min"])
            humidity_max = float(request.form["humidity_max"])

            if temp_min >= temp_max:
                return (
                    jsonify({"error": "Temperature minimum must be less than maximum"}),
                    400,
                )

            if humidity_min >= humidity_max:
                return (
                    jsonify({"error": "Humidity minimum must be less than maximum"}),
                    400,
                )

        except ValueError as e:
            return jsonify({"error": f"Invalid numeric values: {e}"}), 400

        if backend:
            sensor_config = {
                "device_id": request.form["device_id"].strip(),
                "location": request.form["location"].strip(),
                "product_type": request.form.get("product_type", "").strip(),
                "temp_min": temp_min,
                "temp_max": temp_max,
                "humidity_min": humidity_min,
                "humidity_max": humidity_max,
                "alert_email": request.form.get("alert_email", "").strip(),
                "is_active": "is_active" in request.form,
            }

            success = backend.add_sensor(sensor_config)

            if success:
                logger.info(
                    f"Added sensor: {sensor_config['device_id']} at {sensor_config['location']}"
                )
                return jsonify(
                    {
                        "status": "success",
                        "message": f"Sensor {sensor_config['device_id']} added successfully!",
                    }
                )
            else:
                return jsonify({"error": "Failed to add sensor configuration"}), 500
        elif db:
            # Fallback to legacy database
            try:
                success = db.add_sensor_config(request.form.to_dict())
                if success:
                    return jsonify(
                        {
                            "status": "success",
                            "message": f"Sensor {request.form['device_id']} added (legacy mode)!",
                        }
                    )
                else:
                    return jsonify({"error": "Failed to add sensor configuration"}), 500
            except Exception as e:
                return jsonify({"error": f"Legacy database error: {e}"}), 500
        else:
            return jsonify({"error": "No backend available - sensor not saved"}), 503

    except Exception as e:
        logger.error(f"Add sensor error: {e}")
        return jsonify({"error": f"Error adding sensor: {e}"}), 500


# ============================================================================
# SENSOR MANAGEMENT API ENDPOINTS
# ============================================================================


@app.route("/api/sensors", methods=["GET"])
def get_sensors():
    """Get all configured sensors"""
    try:
        if backend:
            sensors = get_safe_data(lambda: backend.settings.get_sensor_config(), [])
            return jsonify({"sensors": sensors})
        elif db:
            sensors = get_safe_data(lambda: db.get_sensor_config(), [])
            return jsonify({"sensors": sensors})
        else:
            return jsonify({"error": "Backend not available"}), 503
    except Exception as e:
        logger.error(f"Error getting sensors: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/sensors", methods=["POST"])
def add_sensor():
    """Add a new sensor configuration"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        required_fields = [
            "device_id",
            "location",
            "product_type",
            "temp_min",
            "temp_max",
            "humidity_min",
            "humidity_max",
        ]
        if not all(field in data for field in required_fields):
            return jsonify({"error": "Missing required fields"}), 400

        if backend:
            success = backend.add_sensor(data)
            if success:
                return jsonify(
                    {"status": "success", "message": "Sensor added successfully"}
                )
            else:
                return jsonify({"error": "Failed to add sensor"}), 500
        else:
            return jsonify({"error": "Backend not available"}), 503
    except Exception as e:
        logger.error(f"Error adding sensor: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/sensors/<device_id>", methods=["GET"])
def get_sensor(device_id):
    """Get specific sensor configuration"""
    try:
        if backend:
            sensor = backend.settings.get_sensor_config(device_id)
            if sensor:
                return jsonify({"sensor": sensor})
            else:
                return jsonify({"error": "Sensor not found"}), 404
        else:
            return jsonify({"error": "Backend not available"}), 503
    except Exception as e:
        logger.error(f"Error getting sensor {device_id}: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/sensors/<device_id>", methods=["DELETE"])
def delete_sensor(device_id):
    """Delete a sensor configuration"""
    try:
        if backend:
            success = backend.settings.delete_sensor_config(device_id)
            if success:
                logger.info(f"Deleted sensor: {device_id}")
                return jsonify(
                    {
                        "status": "success",
                        "message": f"Sensor {device_id} deleted successfully",
                    }
                )
            else:
                return (
                    jsonify({"error": "Failed to delete sensor or sensor not found"}),
                    404,
                )
        else:
            return jsonify({"error": "Backend not available"}), 503
    except Exception as e:
        logger.error(f"Error deleting sensor {device_id}: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/sensors/<device_id>/test", methods=["POST"])
def test_sensor(device_id):
    """Test a sensor connection and configuration"""
    try:
        if backend:
            # Check if sensor exists
            sensor = backend.settings.get_sensor_config(device_id)
            if not sensor:
                return jsonify({"error": "Sensor not found"}), 404

            # Simulate sensor test (in real implementation, this would ping the actual sensor)
            test_result = {
                "device_id": device_id,
                "status": "success",
                "message": "Sensor configuration is valid and accessible",
                "last_reading": "2 minutes ago",
                "connection_status": "online",
            }

            logger.info(f"Tested sensor: {device_id}")
            return jsonify(test_result)
        else:
            return jsonify({"error": "Backend not available"}), 503
    except Exception as e:
        logger.error(f"Error testing sensor {device_id}: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/settings/export", methods=["GET"])
def export_settings():
    """Export all settings and sensor configurations"""
    try:
        if backend:
            # Get all settings and sensor configurations
            settings = get_safe_data(lambda: backend.settings.get_all_settings(), {})
            sensors = get_safe_data(lambda: backend.settings.get_sensor_config(), [])

            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "system_settings": settings,
                "sensor_configurations": sensors,
                "version": "1.0",
            }

            response = jsonify(export_data)
            response.headers["Content-Disposition"] = (
                "attachment; filename=settings_export.json"
            )
            response.headers["Content-Type"] = "application/json"

            logger.info("Settings exported successfully")
            return response
        else:
            return jsonify({"error": "Backend not available"}), 503
    except Exception as e:
        logger.error(f"Error exporting settings: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/settings/import", methods=["POST"])
def import_settings():
    """Import settings and sensor configurations"""
    try:
        if backend:
            if "file" not in request.files:
                return jsonify({"error": "No file provided"}), 400

            file = request.files["file"]
            if file.filename == "":
                return jsonify({"error": "No file selected"}), 400

            try:
                import_data = json.loads(file.read().decode("utf-8"))
            except json.JSONDecodeError:
                return jsonify({"error": "Invalid JSON file"}), 400

            # Validate import data structure
            if not all(
                key in import_data
                for key in ["system_settings", "sensor_configurations"]
            ):
                return jsonify({"error": "Invalid settings file format"}), 400

            # Import system settings
            if import_data.get("system_settings"):
                for key, value in import_data["system_settings"].items():
                    backend.settings.update_setting(key, value)

            # Import sensor configurations
            imported_sensors = 0
            for sensor_config in import_data.get("sensor_configurations", []):
                try:
                    success = backend.add_sensor(sensor_config)
                    if success:
                        imported_sensors += 1
                except Exception as e:
                    logger.warning(
                        f"Failed to import sensor {sensor_config.get('device_id', 'unknown')}: {e}"
                    )

            logger.info(f"Settings imported: {imported_sensors} sensors")
            return jsonify(
                {
                    "status": "success",
                    "message": f"Settings imported successfully. {imported_sensors} sensors added.",
                }
            )
        else:
            return jsonify({"error": "Backend not available"}), 503
    except Exception as e:
        logger.error(f"Error importing settings: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/sensors/<device_id>/data", methods=["GET"])
def get_sensor_data(device_id):
    """Get historical data for a specific sensor"""
    try:
        hours = request.args.get("hours", 24, type=int)

        if backend:
            data = backend.reports.get_sensor_data(device_id, hours=hours)
            return jsonify({"data": data})
        else:
            return jsonify({"error": "Backend not available"}), 503
    except Exception as e:
        logger.error(f"Error getting sensor data for {device_id}: {e}")
        return jsonify({"error": str(e)}), 500


# ============================================================================
# REPORTS API ENDPOINTS
# ============================================================================


@app.route("/api/reports/overview", methods=["GET"])
def get_system_overview():
    """Get system overview report"""
    try:
        hours = request.args.get("hours", 24, type=int)

        if backend:
            overview = backend.reports.get_system_overview(hours=hours)
            return jsonify(overview)
        else:
            return jsonify({"error": "Backend not available"}), 503
    except Exception as e:
        logger.error(f"Error getting system overview: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/reports/sensors", methods=["GET"])
def get_sensor_statistics():
    """Get sensor statistics report"""
    try:
        hours = request.args.get("hours", 24, type=int)

        if backend:
            stats = backend.reports.get_sensor_statistics(hours=hours)
            return jsonify({"statistics": stats})
        else:
            return jsonify({"error": "Backend not available"}), 503
    except Exception as e:
        logger.error(f"Error getting sensor statistics: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/reports/export", methods=["GET"])
def export_data():
    """Export data in various formats"""
    try:
        format_type = request.args.get("format", "json")
        hours = request.args.get("hours", 24, type=int)

        if backend:
            if format_type == "csv":
                csv_data = backend.reports.export_to_csv(hours=hours)
                return (
                    csv_data,
                    200,
                    {
                        "Content-Type": "text/csv",
                        "Content-Disposition": ("attachment; filename=sensor_data.csv"),
                    },
                )
            else:
                data = backend.reports.get_all_data(hours=hours)
                return jsonify({"data": data})
        else:
            return jsonify({"error": "Backend not available"}), 503
    except Exception as e:
        logger.error(f"Error exporting data: {e}")
        return jsonify({"error": str(e)}), 500


# ============================================================================
# ERROR HANDLERS
# ============================================================================


@app.errorhandler(404)
def not_found_error(error):  # noqa: ARG001
    return render_template("base.html"), 404


@app.errorhandler(500)
def internal_error(error):
    logger.error(f"Server Error: {error}")
    return render_template("base.html"), 500


# ============================================================================
# APPLICATION STARTUP
# ============================================================================

if __name__ == "__main__":
    try:
        print("🍎 Food Monitoring System - Clean Application")
        print("=" * 60)
        print("🔧 Starting integrated frontend and backend system...")
        print()

        if backend:
            print("✅ Backend Services Active:")
            print("   • Settings Manager - System and sensor configuration")
            print("   • Alerts Manager - Real-time alerting and notifications")
            print("   • Reports Manager - Analytics and data export")
        else:
            print("⚠️ Backend services not available - running in demo mode")

        if monitoring_core:
            print("✅ Legacy monitoring core available")

        print()

        # Try different ports to find an available one
        ports_to_try = [5000, 8080, 3000, 8000, 5001]
        server_started = False

        for port in ports_to_try:
            try:
                print(f"🌐 Trying to start server on port {port}...")
                print(f"   • Dashboard: http://localhost:{port}")
                print(f"   • Alerts: http://localhost:{port}/alerts")
                print(f"   • API Health: http://localhost:{port}/api/system/health")
                print()
                print("📡 API Endpoints:")
                print(
                    f"   • POST http://localhost:{port}/api/data - "
                    f"Receive sensor data"
                )
                print(
                    f"   • GET http://localhost:{port}/api/dashboard/data - "
                    f"Dashboard data"
                )
                print(
                    f"   • GET http://localhost:{port}/api/system/health - "
                    f"System health"
                )
                print("=" * 60)
                print(f"🚀 COPY THIS URL TO YOUR BROWSER: http://localhost:{port}")
                print("=" * 60)
                print("Press Ctrl+C to stop the server")
                print()

                # Run Flask app with better configuration
                app.run(
                    host="0.0.0.0",
                    port=port,
                    debug=False,
                    use_reloader=False,
                    threaded=True,
                )
                server_started = True
                break

            except OSError as e:
                addr_in_use = "Address already in use" in str(e)
                win_error = "WinError 10048" in str(e)
                if addr_in_use or win_error:
                    print(f"⚠️ Port {port} is already in use, trying next...")
                    continue
                else:
                    print(f"❌ Error starting server on port {port}: {e}")
                    continue
            except Exception as e:
                print(f"❌ Unexpected error on port {port}: {e}")
                continue

        if not server_started:
            print("❌ Could not start server on any available port")
            print("Please check if other applications are using these ports")

    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
    finally:
        logger.info("Food Monitoring System stopped")
