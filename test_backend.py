#!/usr/bin/env python3
"""
Test script to diagnose backend connectivity issues
"""

def test_backend_components():
    """Test each backend component individually"""
    print("🔍 Testing Backend Components...")
    
    # Test 1: Import backend modules
    try:
        from backend import BackendManager
        print("✅ BackendManager import successful")
    except Exception as e:
        print(f"❌ BackendManager import failed: {e}")
        return
    
    # Test 2: Initialize backend
    try:
        backend = BackendManager("test_food_monitoring.db")
        print("✅ Backend initialization successful")
    except Exception as e:
        print(f"❌ Backend initialization failed: {e}")
        return
    
    # Test 3: Test settings manager
    try:
        configs = backend.settings.get_sensor_config()
        print(f"✅ Settings Manager: Found {len(configs)} sensor configs")
    except Exception as e:
        print(f"❌ Settings Manager failed: {e}")
    
    # Test 4: Test reports manager
    try:
        overview = backend.reports.get_system_overview(hours=24)
        print(f"✅ Reports Manager: System overview retrieved")
    except Exception as e:
        print(f"❌ Reports Manager failed: {e}")
    
    # Test 5: Test alerts manager
    try:
        alerts = backend.alerts.get_alerts(limit=5)
        print(f"✅ Alerts Manager: Found {len(alerts)} alerts")
    except Exception as e:
        print(f"❌ Alerts Manager failed: {e}")
    
    # Test 6: Test dashboard data
    try:
        dashboard_data = backend.get_dashboard_data()
        print(f"✅ Dashboard data retrieved successfully")
    except Exception as e:
        print(f"❌ Dashboard data failed: {e}")
    
    print("\n🔧 Testing sensor data processing...")
    
    # Test 7: Test sensor data processing
    test_sensor_data = {
        "device_id": "TEST_SENSOR_001",
        "temperature": 5.5,
        "humidity": 75.0,
        "location": "Test Location",
        "timestamp": "2024-01-01T12:00:00"
    }
    
    try:
        # First add a sensor config
        sensor_config = {
            "device_id": "TEST_SENSOR_001",
            "location": "Test Location",
            "product_type": "Test Product",
            "temp_min": 2.0,
            "temp_max": 8.0,
            "humidity_min": 60.0,
            "humidity_max": 80.0,
            "alert_email": "<EMAIL>"
        }
        
        success = backend.add_sensor(sensor_config)
        if success:
            print("✅ Test sensor config added")
        else:
            print("⚠️ Test sensor config already exists or failed to add")
        
        # Test getting the config
        config = backend.settings.get_sensor_config("TEST_SENSOR_001")
        if config:
            print("✅ Sensor config retrieval successful")
        else:
            print("❌ Sensor config retrieval failed")
            
    except Exception as e:
        print(f"❌ Sensor config test failed: {e}")

if __name__ == "__main__":
    test_backend_components()
