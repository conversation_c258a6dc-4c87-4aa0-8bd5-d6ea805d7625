#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to automatically fix Flake8 line length issues in app_monitor.py
"""

import re

def fix_long_lines(file_path):
    """Fix long lines in the Python file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # List of specific fixes for long lines
    fixes = [
        # Line 108: dashboard_data
        (
            r'(\s+)dashboard_data = get_safe_data\(lambda: backend\.get_dashboard_data\(\), \{\}\)',
            r'\1dashboard_data = get_safe_data(\n\1    lambda: backend.get_dashboard_data(), {}\n\1)'
        ),
        # Line 125: alerts.get_alerts
        (
            r'(\s+)lambda: backend\.alerts\.get_alerts\(acknowledged=False, limit=10\), \[\]',
            r'\1lambda: backend.alerts.get_alerts(\n\1    acknowledged=False, limit=10\n\1), []'
        ),
        # Line 161-162: legacy system calls
        (
            r'(\s+)active_alerts = get_safe_data\(lambda: db\.get_active_alerts\(\), \[\]\)\n(\s+)legacy_stats = get_safe_data\(lambda: db\.get_statistics\(hours=24\), \{\}\)',
            r'\1active_alerts = get_safe_data(\n\1    lambda: db.get_active_alerts(), []\n\1)\n\2legacy_stats = get_safe_data(\n\2    lambda: db.get_statistics(hours=24), {}\n\2)'
        ),
        # Line 166: system_health
        (
            r'(\s+)system_health = \{"health_status": "Legacy Mode", "health_score": 50\}',
            r'\1system_health = {\n\1    "health_status": "Legacy Mode",\n\1    "health_score": 50\n\1}'
        ),
        # Line 292: alerts.get_alerts with limit 100
        (
            r'(\s+)lambda: backend\.alerts\.get_alerts\(acknowledged=False, limit=100\), \[\]',
            r'\1lambda: backend.alerts.get_alerts(\n\1    acknowledged=False, limit=100\n\1), []'
        ),
        # Line 351: sensor_config
        (
            r'(\s+)sensor_config = backend\.settings\.get_sensor_config\(data\["device_id"\]\)',
            r'\1sensor_config = backend.settings.get_sensor_config(\n\1    data["device_id"]\n\1)'
        ),
    ]
    
    # Apply fixes
    for pattern, replacement in fixes:
        content = re.sub(pattern, replacement, content)
    
    # Fix JSON response messages
    json_fixes = [
        (
            r'(\s+)\{"status": "success", "message": "Data received and processed"\}',
            r'\1{\n\1    "status": "success",\n\1    "message": "Data received and processed"\n\1}'
        ),
        (
            r'(\s+)jsonify\(\{"status": "success", "message": "Data received"\}\),',
            r'\1jsonify({\n\1    "status": "success",\n\1    "message": "Data received"\n\1}),'
        ),
        (
            r'(\s+)\{"status": "success", "message": "Data received \(legacy error\)"\}',
            r'\1{\n\1    "status": "success",\n\1    "message": "Data received (legacy error)"\n\1}'
        ),
        (
            r'(\s+)\{"status": "success", "message": "Data received \(no processing\)"\}',
            r'\1{\n\1    "status": "success",\n\1    "message": "Data received (no processing)"\n\1}'
        ),
        (
            r'(\s+)return jsonify\(\{"health_status": "Legacy Mode", "health_score": 50\}\)',
            r'\1return jsonify({\n\1    "health_status": "Legacy Mode",\n\1    "health_score": 50\n\1})'
        ),
    ]
    
    for pattern, replacement in json_fixes:
        content = re.sub(pattern, replacement, content)
    
    # Fix function parameter lines
    param_fixes = [
        (
            r'(\s+)lambda: backend\.get_system_health\(\), \{"health_status": "Unknown"\}',
            r'\1lambda: backend.get_system_health(),\n\1{"health_status": "Unknown"}'
        ),
        (
            r'(\s+)lambda: monitoring_core\.get_system_status\(\), \{"status": "Unknown"\}',
            r'\1lambda: monitoring_core.get_system_status(),\n\1{"status": "Unknown"}'
        ),
    ]
    
    for pattern, replacement in param_fixes:
        content = re.sub(pattern, replacement, content)
    
    # Fix print statements
    print_fixes = [
        (
            r'(\s+)print\(f"   • API Health: http://localhost:\{port\}/api/system/health"\)',
            r'\1print(\n\1    f"   • API Health: http://localhost:{port}/api/system/health"\n\1)'
        ),
        (
            r'(\s+)print\(f"🚀 COPY THIS URL TO YOUR BROWSER: http://localhost:\{port\}"\)',
            r'\1print(\n\1    f"🚀 COPY THIS URL TO YOUR BROWSER: http://localhost:{port}"\n\1)'
        ),
    ]
    
    for pattern, replacement in print_fixes:
        content = re.sub(pattern, replacement, content)
    
    # Fix the OSError condition
    content = re.sub(
        r'(\s+)if "Address already in use" in str\(e\) or "WinError 10048" in str\(e\):\n(\s+)print\(f"⚠️ Port \{port\} is already in use, trying next port\.\.\."\)',
        r'\1if ("Address already in use" in str(e) or\n\1        "WinError 10048" in str(e)):\n\2print(\n\2    f"⚠️ Port {port} is already in use, trying next port..."\n\2)',
        content
    )
    
    # Write the fixed content back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Fixed long lines in app_monitor.py")

if __name__ == "__main__":
    fix_long_lines("app_monitor.py")
