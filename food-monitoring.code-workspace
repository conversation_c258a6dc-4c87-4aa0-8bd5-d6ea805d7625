{"folders": [{"name": "🍎 Food Monitoring System", "path": "."}], "settings": {"python.defaultInterpreterPath": "python", "python.terminal.activateEnvironment": true, "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.testing.pytestEnabled": true, "files.associations": {"*.html": "html", "*.css": "css", "*.js": "javascript", "*.json": "jsonc", "*.md": "markdown", "*.py": "python", "*.yml": "yaml", "*.yaml": "yaml", "Dockerfile": "dockerfile", "Makefile": "makefile", ".env*": "properties"}, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/.pytest_cache": true, "**/node_modules": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/*.egg-info": true, "**/dist": true, "**/build": true}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "editor.rulers": [88, 100], "editor.tabSize": 4, "editor.insertSpaces": true, "terminal.integrated.env.windows": {"PYTHONPATH": "${workspaceFolder}"}, "terminal.integrated.env.linux": {"PYTHONPATH": "${workspaceFolder}"}, "terminal.integrated.env.osx": {"PYTHONPATH": "${workspaceFolder}"}}, "launch": {"version": "0.2.0", "configurations": [{"name": "🍎 Launch Food Monitor App", "type": "python", "request": "launch", "program": "${workspaceFolder}/app_monitor.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1", "PYTHONPATH": "${workspaceFolder}"}, "args": [], "justMyCode": true, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true}, {"name": "🚀 Launch with Auto Setup", "type": "python", "request": "launch", "program": "${workspaceFolder}/start_monitor.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1", "PYTHONPATH": "${workspaceFolder}"}, "args": [], "justMyCode": true, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true}, {"name": "🔧 Debug Mode - Food Monitor", "type": "python", "request": "launch", "program": "${workspaceFolder}/app_monitor.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1", "PYTHONPATH": "${workspaceFolder}", "LOG_LEVEL": "DEBUG"}, "args": [], "justMyCode": false, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true, "breakpointValidation": true, "logToFile": true}]}, "tasks": {"version": "2.0.0", "tasks": [{"label": "🍎 Start Food Monitor", "type": "shell", "command": "python", "args": ["app_monitor.py"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1", "PYTHONPATH": "${workspaceFolder}"}}, "problemMatcher": []}, {"label": "🧪 Run Tests", "type": "shell", "command": "python", "args": ["test_app_monitor.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, "problemMatcher": []}]}, "extensions": {"recommendations": ["ms-python.python", "ms-python.flake8", "ms-python.black-formatter", "ms-python.isort", "bradlc.vscode-tailwindcss", "formulahendry.auto-rename-tag", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.makefile-tools"]}}