#!/usr/bin/env python3
"""
Integration test to verify sensors, reports, and settings are properly connected
"""

import requests
import json
import time
from datetime import datetime

def test_full_integration():
    """Test the complete integration of sensors, reports, and settings"""
    base_url = "http://localhost:5000"
    
    print("🔧 Testing Full Integration of Sensors, Reports, and Settings")
    print("=" * 60)
    
    # Test 1: Add a sensor configuration
    print("\n1️⃣ Testing Sensor Configuration...")
    sensor_config = {
        "device_id": "INTEGRATION_TEST_001",
        "location": "Test Freezer A",
        "product_type": "Fresh Meat",
        "temp_min": 2.0,
        "temp_max": 8.0,
        "humidity_min": 60.0,
        "humidity_max": 80.0,
        "alert_email": "<EMAIL>"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/sensors",
            json=sensor_config,
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        if response.status_code == 200:
            print("✅ Sensor configuration added successfully")
        else:
            print(f"⚠️ Sensor configuration failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Error adding sensor: {e}")
        return False
    
    # Test 2: Verify sensor was added
    print("\n2️⃣ Testing Sensor Retrieval...")
    try:
        response = requests.get(
            f"{base_url}/api/sensors/INTEGRATION_TEST_001",
            timeout=5
        )
        if response.status_code == 200:
            sensor_data = response.json()
            print("✅ Sensor configuration retrieved successfully")
            print(f"   Device ID: {sensor_data['sensor']['device_id']}")
            print(f"   Location: {sensor_data['sensor']['location']}")
        else:
            print(f"⚠️ Sensor retrieval failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error retrieving sensor: {e}")
    
    # Test 3: Send sensor data
    print("\n3️⃣ Testing Sensor Data Submission...")
    test_readings = [
        {"temperature": 3.5, "humidity": 65.0},  # Normal
        {"temperature": 1.0, "humidity": 55.0},  # Low temp, low humidity
        {"temperature": 10.0, "humidity": 85.0}, # High temp, high humidity
        {"temperature": 5.0, "humidity": 70.0},  # Normal
    ]
    
    for i, reading in enumerate(test_readings):
        sensor_data = {
            "device_id": "INTEGRATION_TEST_001",
            "temperature": reading["temperature"],
            "humidity": reading["humidity"],
            "location": "Test Freezer A",
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            response = requests.post(
                f"{base_url}/api/data",
                json=sensor_data,
                headers={"Content-Type": "application/json"},
                timeout=5
            )
            if response.status_code == 200:
                print(f"✅ Reading {i+1}: {reading['temperature']}°C, {reading['humidity']}% - Sent")
            else:
                print(f"⚠️ Reading {i+1} failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Error sending reading {i+1}: {e}")
        
        time.sleep(0.5)  # Small delay between readings
    
    # Test 4: Check alerts were created
    print("\n4️⃣ Testing Alert Generation...")
    try:
        response = requests.get(f"{base_url}/alerts", timeout=5)
        if response.status_code == 200:
            print("✅ Alerts page accessible")
        else:
            print(f"⚠️ Alerts page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error accessing alerts: {e}")
    
    # Test 5: Check reports
    print("\n5️⃣ Testing Reports Generation...")
    try:
        response = requests.get(
            f"{base_url}/api/reports/overview?hours=1",
            timeout=5
        )
        if response.status_code == 200:
            overview = response.json()
            print("✅ System overview report generated")
            print(f"   Total sensors: {overview.get('total_sensors', 'N/A')}")
            print(f"   Total readings: {overview.get('total_readings', 'N/A')}")
        else:
            print(f"⚠️ System overview failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting system overview: {e}")
    
    # Test 6: Check sensor statistics
    print("\n6️⃣ Testing Sensor Statistics...")
    try:
        response = requests.get(
            f"{base_url}/api/reports/sensors?hours=1",
            timeout=5
        )
        if response.status_code == 200:
            stats = response.json()
            print("✅ Sensor statistics generated")
            print(f"   Statistics count: {len(stats.get('statistics', []))}")
        else:
            print(f"⚠️ Sensor statistics failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting sensor statistics: {e}")
    
    # Test 7: Check sensor data retrieval
    print("\n7️⃣ Testing Sensor Data Retrieval...")
    try:
        response = requests.get(
            f"{base_url}/api/sensors/INTEGRATION_TEST_001/data?hours=1",
            timeout=5
        )
        if response.status_code == 200:
            data = response.json()
            readings = data.get('data', [])
            print(f"✅ Retrieved {len(readings)} sensor readings")
            if readings:
                latest = readings[0]
                print(f"   Latest reading: {latest.get('temperature')}°C, {latest.get('humidity')}%")
        else:
            print(f"⚠️ Sensor data retrieval failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error retrieving sensor data: {e}")
    
    # Test 8: Test data export
    print("\n8️⃣ Testing Data Export...")
    try:
        response = requests.get(
            f"{base_url}/api/reports/export?format=json&hours=1",
            timeout=5
        )
        if response.status_code == 200:
            export_data = response.json()
            print(f"✅ Data export successful")
            print(f"   Exported records: {len(export_data.get('data', []))}")
        else:
            print(f"⚠️ Data export failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error exporting data: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Integration test completed!")
    print("Check the dashboard at http://localhost:5000 to see the results")
    return True

if __name__ == "__main__":
    print("⏳ Waiting 3 seconds for server to be ready...")
    time.sleep(3)
    test_full_integration()
