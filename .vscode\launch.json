{"version": "0.2.0", "configurations": [{"name": "Attach to Chrome", "port": 9222, "request": "attach", "type": "chrome", "webRoot": "${workspaceFolder}"}, {"name": "Python Debugger: Current File", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal"}, {"name": "🍎 Launch Food Monitor App", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/app_monitor.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1", "PYTHONPATH": "${workspaceFolder}"}, "args": [], "justMyCode": true, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true}, {"name": "🚀 Launch with Auto Setup", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/start_monitor.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1", "PYTHONPATH": "${workspaceFolder}"}, "args": [], "justMyCode": true, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true}, {"name": "🧪 Run Tests", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/test_app_monitor.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": [], "justMyCode": true, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true}, {"name": "🔧 Debug Mode - Food Monitor", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/app_monitor.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1", "PYTHONPATH": "${workspaceFolder}", "LOG_LEVEL": "DEBUG"}, "args": [], "justMyCode": false, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true, "logToFile": true}, {"name": "🏗️ Launch New Architecture", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/run.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1", "PYTHONPATH": "${workspaceFolder}"}, "args": ["--debug"], "justMyCode": true, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true}, {"name": "📊 Launch with Production Settings", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/app_monitor.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"FLASK_ENV": "production", "FLASK_DEBUG": "0", "PYTHONPATH": "${workspaceFolder}"}, "args": [], "justMyCode": true, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true}, {"name": "🔍 Debug Backend Only", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/backend/__init__.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": [], "justMyCode": true, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true}, {"name": "🌐 Launch on Custom Port", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/app_monitor.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1", "PYTHONPATH": "${workspaceFolder}", "PORT": "8080"}, "args": [], "justMyCode": true, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true}]}