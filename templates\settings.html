{% extends "base.html" %}

{% block title %}Settings - Food Monitoring System{% endblock %}

{% block extra_css %}
<style>
    .settings-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .settings-section {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 4px solid #667eea;
    }

    .settings-section h4 {
        color: #667eea;
        margin-bottom: 1rem;
        font-size: 1.1rem;
    }

    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .sensor-form {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .sensor-form h3 {
        color: white;
        margin-bottom: 1rem;
    }

    .sensor-form .form-group label {
        color: white;
        font-weight: bold;
    }

    .sensor-form .form-group input,
    .sensor-form .form-group select {
        background: rgba(255, 255, 255, 0.9);
        border: none;
        color: #333;
    }

    .sensor-form .form-group input:focus,
    .sensor-form .form-group select:focus {
        background: white;
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
    }

    .sensor-table {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .sensor-table th {
        background: #667eea;
        color: white;
        padding: 1rem;
        font-weight: bold;
    }

    .sensor-table td {
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
    }

    .sensor-table tr:hover {
        background: #f8f9fa;
    }

    .quick-setup {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 2rem;
    }

    .quick-setup h4 {
        color: #1976d2;
        margin-bottom: 0.5rem;
    }

    .preset-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        margin-top: 1rem;
    }

    .preset-btn {
        background: #2196f3;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: background 0.3s;
    }

    .preset-btn:hover {
        background: #1976d2;
    }

    .validation-message {
        background: #fff3cd;
        border: 1px solid #ffc107;
        color: #856404;
        padding: 0.75rem;
        border-radius: 5px;
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }

    .success-message {
        background: #d4edda;
        border: 1px solid #28a745;
        color: #155724;
        padding: 0.75rem;
        border-radius: 5px;
        margin-bottom: 1rem;
    }

    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .checkbox-group input[type="checkbox"] {
        width: auto;
        margin: 0;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    function applyPreset(productType) {
        const presets = {
            'Fresh Meat': { temp_min: -2, temp_max: 4, humidity_min: 80, humidity_max: 95 },
            'Dairy Products': { temp_min: 1, temp_max: 4, humidity_min: 75, humidity_max: 85 },
            'Fresh Vegetables': { temp_min: 0, temp_max: 8, humidity_min: 85, humidity_max: 95 },
            'Frozen Foods': { temp_min: -25, temp_max: -18, humidity_min: 70, humidity_max: 90 },
            'Fruits': { temp_min: 2, temp_max: 10, humidity_min: 80, humidity_max: 90 }
        };

        const preset = presets[productType];
        if (preset) {
            document.querySelector('input[name="temp_min"]').value = preset.temp_min;
            document.querySelector('input[name="temp_max"]').value = preset.temp_max;
            document.querySelector('input[name="humidity_min"]').value = preset.humidity_min;
            document.querySelector('input[name="humidity_max"]').value = preset.humidity_max;
            document.querySelector('select[name="product_type"]').value = productType;
        }
    }

    function validateSensorForm() {
        const deviceId = document.querySelector('input[name="device_id"]').value;
        const location = document.querySelector('input[name="location"]').value;
        const tempMin = parseFloat(document.querySelector('input[name="temp_min"]').value);
        const tempMax = parseFloat(document.querySelector('input[name="temp_max"]').value);
        const humidityMin = parseFloat(document.querySelector('input[name="humidity_min"]').value);
        const humidityMax = parseFloat(document.querySelector('input[name="humidity_max"]').value);

        let errors = [];

        if (!deviceId || deviceId.length < 3) {
            errors.push("Device ID must be at least 3 characters long");
        }

        if (!location || location.length < 3) {
            errors.push("Location must be at least 3 characters long");
        }

        if (tempMin >= tempMax) {
            errors.push("Minimum temperature must be less than maximum temperature");
        }

        if (humidityMin >= humidityMax) {
            errors.push("Minimum humidity must be less than maximum humidity");
        }

        if (humidityMin < 0 || humidityMax > 100) {
            errors.push("Humidity values must be between 0 and 100");
        }

        const validationDiv = document.getElementById('validation-messages');
        if (errors.length > 0) {
            validationDiv.innerHTML = '<div class="validation-message">⚠️ ' + errors.join('<br>⚠️ ') + '</div>';
            return false;
        } else {
            validationDiv.innerHTML = '';
            return true;
        }
    }

    function deleteSensor(deviceId) {
        if (confirm(`Are you sure you want to delete sensor ${deviceId}? This action cannot be undone.`)) {
            fetch(`/api/sensors/${deviceId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        alert(`✅ Sensor ${deviceId} deleted successfully!`);
                        location.reload();
                    } else {
                        alert(`❌ Error deleting sensor: ${data.error || 'Unknown error'}`);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert(`❌ Network error deleting sensor: ${error.message}`);
                });
        }
    }

    function testSensor(deviceId) {
        fetch(`/api/sensors/${deviceId}/test`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert(`✅ Sensor ${deviceId} test completed successfully!`);
                } else {
                    alert(`⚠️ Sensor test result: ${data.message || 'Test completed with warnings'}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert(`❌ Error testing sensor: ${error.message}`);
            });
    }

    function exportSettings() {
        fetch('/api/settings/export')
            .then(response => {
                if (response.ok) {
                    return response.blob();
                }
                throw new Error('Export failed');
            })
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = 'settings_export.json';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                alert('✅ Settings exported successfully!');
            })
            .catch(error => {
                console.error('Error:', error);
                alert(`❌ Export failed: ${error.message}`);
            });
    }

    function importSettings() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = function (event) {
            const file = event.target.files[0];
            if (file) {
                const formData = new FormData();
                formData.append('file', file);

                fetch('/api/settings/import', {
                    method: 'POST',
                    body: formData
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            alert('✅ Settings imported successfully!');
                            location.reload();
                        } else {
                            alert(`❌ Import failed: ${data.error || 'Unknown error'}`);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert(`❌ Import failed: ${error.message}`);
                    });
            }
        };
        input.click();
    }

    // Form validation on submit
    document.addEventListener('DOMContentLoaded', function () {
        const sensorForm = document.querySelector('form[action="/settings/sensor/add"]');
        if (sensorForm) {
            sensorForm.addEventListener('submit', function (e) {
                if (!validateSensorForm()) {
                    e.preventDefault();
                }
            });
        }
    });
</script>
{% endblock %}

{% block content %}
<div class="card">
    <h2>⚙️ System Settings</h2>
    <p>Configure monitoring system parameters, sensor settings, and system preferences.</p>

    {% if get_flashed_messages() %}
    {% for category, message in get_flashed_messages(with_categories=true) %}
    <div class="success-message">
        ✅ {{ message }}
    </div>
    {% endfor %}
    {% endif %}

    <div style="display: flex; gap: 1rem; margin-bottom: 2rem; flex-wrap: wrap;">
        <button class="btn" onclick="location.reload()">🔄 Refresh Settings</button>
        <button class="btn" onclick="exportSettings()">📤 Export Settings</button>
        <button class="btn" onclick="importSettings()">📥 Import Settings</button>
        <button class="btn btn-success" onclick="window.open('/', '_blank')">📊 View Dashboard</button>
    </div>
</div>

<!-- System Configuration -->
<div class="settings-grid">
    <div class="settings-section">
        <h4>🔧 System Configuration</h4>
        <form method="POST" action="/settings/update">
            <div class="form-group">
                <label>Company Name:</label>
                <input type="text" name="setting_company_name" value="{{ settings.get('company_name', '') }}"
                    placeholder="Your Company Name">
            </div>
            <div class="form-group">
                <label>Alert Email:</label>
                <input type="email" name="setting_alert_email" value="{{ settings.get('alert_email', '') }}"
                    placeholder="<EMAIL>">
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>SMTP Server:</label>
                    <input type="text" name="setting_smtp_server" value="{{ settings.get('smtp_server', '') }}"
                        placeholder="smtp.gmail.com">
                </div>
                <div class="form-group">
                    <label>SMTP Port:</label>
                    <input type="number" name="setting_smtp_port" value="{{ settings.get('smtp_port', '') }}"
                        placeholder="587">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>Check Interval (seconds):</label>
                    <input type="number" name="setting_check_interval" value="{{ settings.get('check_interval', '') }}"
                        min="30" max="3600" placeholder="60">
                </div>
                <div class="form-group">
                    <label>Data Retention (days):</label>
                    <input type="number" name="setting_data_retention_days"
                        value="{{ settings.get('data_retention_days', '') }}" min="1" max="365" placeholder="30">
                </div>
            </div>
            <button type="submit" class="btn btn-success">💾 Save System Settings</button>
        </form>
    </div>

    <div class="settings-section">
        <h4>📊 Dashboard Configuration</h4>
        <form method="POST" action="/settings/update">
            <div class="form-group">
                <label>Dashboard Refresh Interval (seconds):</label>
                <input type="number" name="setting_dashboard_refresh"
                    value="{{ settings.get('dashboard_refresh', '') }}" min="10" max="300" placeholder="30">
            </div>
            <div class="form-group">
                <label>Default Time Range for Reports:</label>
                <select name="setting_default_time_range">
                    <option value="24" {% if settings.get('default_time_range')=='24' %}selected{% endif %}>Last 24
                        Hours</option>
                    <option value="168" {% if settings.get('default_time_range')=='168' %}selected{% endif %}>Last 7
                        Days</option>
                    <option value="720" {% if settings.get('default_time_range')=='720' %}selected{% endif %}>Last 30
                        Days</option>
                </select>
            </div>
            <div class="form-group">
                <label>Temperature Unit:</label>
                <select name="setting_temperature_unit">
                    <option value="celsius" {% if settings.get('temperature_unit')=='celsius' %}selected{% endif %}>
                        Celsius (°C)</option>
                    <option value="fahrenheit" {% if settings.get('temperature_unit')=='fahrenheit' %}selected{% endif
                        %}>Fahrenheit (°F)</option>
                </select>
            </div>
            <button type="submit" class="btn btn-success">💾 Save Dashboard Settings</button>
        </form>
    </div>
</div>

<!-- Add New Sensor -->
<div class="card sensor-form">
    <h3>📡 Add New Sensor</h3>

    <div class="quick-setup">
        <h4>⚡ Quick Setup</h4>
        <p>Use preset values for common product types:</p>
        <div class="preset-buttons">
            <button type="button" class="preset-btn" onclick="applyPreset('Fresh Meat')">🥩 Fresh Meat</button>
            <button type="button" class="preset-btn" onclick="applyPreset('Dairy Products')">🥛 Dairy</button>
            <button type="button" class="preset-btn" onclick="applyPreset('Fresh Vegetables')">🥬 Vegetables</button>
            <button type="button" class="preset-btn" onclick="applyPreset('Frozen Foods')">🧊 Frozen</button>
            <button type="button" class="preset-btn" onclick="applyPreset('Fruits')">🍎 Fruits</button>
        </div>
    </div>

    <form method="POST" action="/settings/sensor/add">
        <div class="form-row">
            <div class="form-group">
                <label>Device ID:</label>
                <input type="text" name="device_id" required placeholder="e.g., SENSOR_006" pattern="[A-Z0-9_]+"
                    title="Use uppercase letters, numbers, and underscores only">
            </div>
            <div class="form-group">
                <label>Location:</label>
                <input type="text" name="location" required placeholder="e.g., Cold Storage Room B">
            </div>
        </div>

        <div class="form-group">
            <label>Product Type:</label>
            <select name="product_type">
                <option value="">Select Product Type</option>
                {% for product in product_types %}
                <option value="{{ product.name }}">{{ product.name }} - {{ product.description }}</option>
                {% endfor %}
            </select>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label>Min Temperature (°C):</label>
                <input type="number" step="0.1" name="temp_min" required placeholder="-2.0">
            </div>
            <div class="form-group">
                <label>Max Temperature (°C):</label>
                <input type="number" step="0.1" name="temp_max" required placeholder="4.0">
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label>Min Humidity (%):</label>
                <input type="number" step="0.1" name="humidity_min" required min="0" max="100" placeholder="80.0">
            </div>
            <div class="form-group">
                <label>Max Humidity (%):</label>
                <input type="number" step="0.1" name="humidity_max" required min="0" max="100" placeholder="95.0">
            </div>
        </div>

        <div class="form-group">
            <label>Alert Email (optional):</label>
            <input type="email" name="alert_email" placeholder="Optional specific email for this sensor">
        </div>

        <div class="checkbox-group">
            <input type="checkbox" name="is_active" id="is_active" checked>
            <label for="is_active">Activate sensor immediately</label>
        </div>

        <div id="validation-messages"></div>

        <button type="submit" class="btn btn-success" style="margin-top: 1rem;">➕ Add Sensor</button>
    </form>
</div>

<!-- Current Sensors -->
<div class="card">
    <h3>📋 Current Sensor Configurations</h3>
    {% if sensors %}
    <div class="sensor-table">
        <table class="table">
            <thead>
                <tr>
                    <th>Device ID</th>
                    <th>Location</th>
                    <th>Product Type</th>
                    <th>Temperature Range</th>
                    <th>Humidity Range</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for sensor in sensors %}
                <tr>
                    <td><strong>{{ sensor.device_id }}</strong></td>
                    <td>{{ sensor.location }}</td>
                    <td>{{ sensor.product_type or 'Not specified' }}</td>
                    <td>{{ sensor.temp_min }}°C to {{ sensor.temp_max }}°C</td>
                    <td>{{ sensor.humidity_min }}% to {{ sensor.humidity_max }}%</td>
                    <td>
                        {% if sensor.is_active %}
                        <span class="status-online">ACTIVE</span>
                        {% else %}
                        <span class="status-offline">INACTIVE</span>
                        {% endif %}
                    </td>
                    <td>
                        <button class="btn" onclick="testSensor('{{ sensor.device_id }}')"
                            style="padding: 0.25rem 0.5rem; font-size: 0.8rem; margin: 0.1rem;">🔧 Test</button>
                        <button class="btn btn-danger" onclick="deleteSensor('{{ sensor.device_id }}')"
                            style="padding: 0.25rem 0.5rem; font-size: 0.8rem; margin: 0.1rem;">🗑️ Delete</button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div style="text-align: center; padding: 3rem; color: #666;">
        <div style="font-size: 3rem; margin-bottom: 1rem;">📡</div>
        <h4>No Sensors Configured</h4>
        <p>Add your first sensor using the form above to start monitoring.</p>
    </div>
    {% endif %}
</div>

<!-- Product Types Reference -->
<div class="card product-reference">
    <h3>📚 Product Type Reference</h3>
    <p style="color: rgba(255,255,255,0.9); margin-bottom: 1rem;">Recommended temperature and humidity ranges for
        different food products:</p>
    <table class="table">
        <thead>
            <tr>
                <th>Product Type</th>
                <th>Description</th>
                <th>Temperature Range</th>
                <th>Humidity Range</th>
                <th>Shelf Life</th>
            </tr>
        </thead>
        <tbody>
            {% for product in product_types %}
            <tr>
                <td><strong>{{ product.name }}</strong></td>
                <td>{{ product.description }}</td>
                <td>{{ product.temp_min }}°C to {{ product.temp_max }}°C</td>
                <td>{{ product.humidity_min }}% to {{ product.humidity_max }}%</td>
                <td>{{ product.shelf_life_hours }} hours</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- System Information -->
<div class="card">
    <h3>ℹ️ System Information</h3>
    <div class="settings-grid">
        <div class="settings-section">
            <h4>📊 Current Status</h4>
            <ul style="list-style: none; padding: 0;">
                <li style="padding: 0.5rem 0; border-bottom: 1px solid #dee2e6;">
                    <strong>Total Sensors:</strong> {{ sensors|length }}
                </li>
                <li style="padding: 0.5rem 0; border-bottom: 1px solid #dee2e6;">
                    <strong>Active Sensors:</strong> {{ sensors|selectattr('is_active')|list|length }}
                </li>
                <li style="padding: 0.5rem 0; border-bottom: 1px solid #dee2e6;">
                    <strong>Product Types:</strong> {{ product_types|length }}
                </li>
                <li style="padding: 0.5rem 0;">
                    <strong>System Version:</strong> 1.0.0
                </li>
            </ul>
        </div>

        <div class="settings-section">
            <h4>🔧 Maintenance</h4>
            <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                <button class="btn" onclick="alert('Database cleanup will be implemented in a future version.')">🗃️
                    Clean Database</button>
                <button class="btn" onclick="alert('System backup will be implemented in a future version.')">💾 Backup
                    System</button>
                <button class="btn" onclick="alert('System restore will be implemented in a future version.')">📥
                    Restore System</button>
                <button class="btn btn-danger"
                    onclick="alert('System reset will be implemented in a future version.')">🔄 Reset System</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}