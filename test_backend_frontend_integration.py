#!/usr/bin/env python3
"""
Test Backend-Frontend Integration
Comprehensive test to verify all connections between backend and frontend
"""

import json
import requests
import time
from datetime import datetime


def test_backend_frontend_integration():
    """Test complete backend-frontend integration"""
    base_url = "http://localhost:5000"
    
    print("🔧 Testing Backend-Frontend Integration...")
    print("=" * 60)
    
    # Test 1: Backend Health Check
    print("\n1️⃣ Testing Backend Health...")
    try:
        response = requests.get(f"{base_url}/api/system/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Backend health: {health_data.get('health_status', 'Unknown')}")
        else:
            print(f"⚠️ Backend health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Backend health check error: {e}")
    
    # Test 2: Settings Page Integration
    print("\n2️⃣ Testing Settings Page...")
    try:
        response = requests.get(f"{base_url}/settings", timeout=5)
        if response.status_code == 200:
            print("✅ Settings page loads successfully")
        else:
            print(f"❌ Settings page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Settings page error: {e}")
    
    # Test 3: Sensors API
    print("\n3️⃣ Testing Sensors API...")
    try:
        response = requests.get(f"{base_url}/api/sensors", timeout=5)
        if response.status_code == 200:
            sensors_data = response.json()
            sensors = sensors_data.get('sensors', [])
            print(f"✅ Sensors API: {len(sensors)} sensors found")
        else:
            print(f"❌ Sensors API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Sensors API error: {e}")
    
    # Test 4: Add Sensor Configuration
    print("\n4️⃣ Testing Add Sensor...")
    test_sensor = {
        "device_id": "TEST_INTEGRATION_001",
        "location": "Integration Test Lab",
        "product_type": "Test Product",
        "temp_min": "0",
        "temp_max": "5",
        "humidity_min": "70",
        "humidity_max": "90",
        "alert_email": "<EMAIL>",
        "is_active": "on"
    }
    
    try:
        response = requests.post(
            f"{base_url}/settings/sensor/add",
            data=test_sensor,
            timeout=5
        )
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                print("✅ Sensor added successfully")
            else:
                print(f"⚠️ Sensor add result: {result.get('message', 'Unknown')}")
        else:
            print(f"❌ Add sensor failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Add sensor error: {e}")
    
    # Test 5: Test Sensor Functionality
    print("\n5️⃣ Testing Sensor Test Function...")
    try:
        response = requests.post(
            f"{base_url}/api/sensors/TEST_INTEGRATION_001/test",
            timeout=5
        )
        if response.status_code == 200:
            test_result = response.json()
            print(f"✅ Sensor test: {test_result.get('message', 'Success')}")
        else:
            print(f"⚠️ Sensor test failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Sensor test error: {e}")
    
    # Test 6: Reports Page Integration
    print("\n6️⃣ Testing Reports Page...")
    try:
        response = requests.get(f"{base_url}/reports", timeout=5)
        if response.status_code == 200:
            print("✅ Reports page loads successfully")
        else:
            print(f"❌ Reports page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Reports page error: {e}")
    
    # Test 7: Alerts Page Integration
    print("\n7️⃣ Testing Alerts Page...")
    try:
        response = requests.get(f"{base_url}/alerts", timeout=5)
        if response.status_code == 200:
            print("✅ Alerts page loads successfully")
        else:
            print(f"❌ Alerts page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Alerts page error: {e}")
    
    # Test 8: Sensor Data Submission
    print("\n8️⃣ Testing Sensor Data Submission...")
    sensor_data = {
        "device_id": "TEST_INTEGRATION_001",
        "temperature": 3.5,
        "humidity": 75.0,
        "location": "Integration Test Lab",
        "product_type": "Test Product",
        "timestamp": datetime.now().isoformat()
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/data",
            json=sensor_data,
            timeout=5
        )
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Sensor data submitted: {result.get('message', 'Success')}")
        else:
            print(f"❌ Sensor data submission failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Sensor data submission error: {e}")
    
    # Test 9: Settings Export
    print("\n9️⃣ Testing Settings Export...")
    try:
        response = requests.get(f"{base_url}/api/settings/export", timeout=5)
        if response.status_code == 200:
            export_data = response.json()
            print(f"✅ Settings export: {len(export_data.get('sensor_configurations', []))} sensors exported")
        else:
            print(f"❌ Settings export failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Settings export error: {e}")
    
    # Test 10: Cleanup - Delete Test Sensor
    print("\n🔟 Cleaning up test sensor...")
    try:
        response = requests.delete(
            f"{base_url}/api/sensors/TEST_INTEGRATION_001",
            timeout=5
        )
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Test sensor deleted: {result.get('message', 'Success')}")
        else:
            print(f"⚠️ Test sensor deletion failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Test sensor deletion error: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Backend-Frontend Integration Test Complete!")
    print("=" * 60)


if __name__ == "__main__":
    print("🚀 Starting Backend-Frontend Integration Test...")
    print("⚠️ Make sure the application is running on http://localhost:5000")
    print()
    
    # Wait a moment for user to confirm
    input("Press Enter to continue with the test...")
    
    test_backend_frontend_integration()
