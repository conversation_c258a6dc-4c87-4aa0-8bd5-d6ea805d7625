#!/usr/bin/env python3
"""
Food Monitoring System - Main Flask Application
Complete integration of frontend templates with backend services
"""

import json
import logging
import os
import sqlite3
import sys
from datetime import datetime, timedelta

from flask import (
    Flask,
    Response,
    flash,
    jsonify,
    redirect,
    render_template,
    request,
    url_for,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = "food-monitoring-secret-key-change-this"

# Initialize backend system
try:
    from backend import AlertSeverity, AlertType, BackendManager

    backend = BackendManager("food_monitoring.db")
    print("✅ Backend system initialized successfully")
    print("   • Settings Manager - System and sensor configuration")
    print("   • Alerts Manager - Real-time alerting and notifications")
    print("   • Reports Manager - Analytics and data export")
except Exception as e:
    print(f"❌ Error initializing backend: {e}")
    backend = None

# Fallback components for compatibility
try:
    from alert_system import AlertSystem
    from database import DatabaseManager
    from monitoring_core import MonitoringCore

    db = DatabaseManager()
    monitoring_core = MonitoringCore()
    alert_system = AlertSystem(monitoring_core.config)
    print("✅ Legacy components initialized successfully")
except Exception as e:
    print(f"⚠️ Legacy components not available: {e}")
    db = None
    monitoring_core = None
    alert_system = None

# ============================================================================
# DASHBOARD ROUTES WITH INTEGRATED BACKEND
# ============================================================================


@app.route("/")
def dashboard():
    """Enhanced dashboard with integrated backend data"""
    try:
        if backend:
            # Get comprehensive dashboard data from backend
            try:
                dashboard_data = backend.get_dashboard_data()
            except Exception as e:
                logger.error(f"Error getting dashboard data: {e}")
                dashboard_data = {}

            # Get system health
            try:
                system_health = backend.get_system_health()
            except Exception as e:
                logger.error(f"Error getting system health: {e}")
                system_health = {"health_status": "Unknown", "health_score": 0}

            # Get recent alerts
            try:
                active_alerts = backend.alerts.get_alerts(acknowledged=False, limit=10)
            except Exception as e:
                logger.error(f"Error getting alerts: {e}")
                active_alerts = []

            # Get sensor statistics
            try:
                sensor_stats = backend.reports.get_sensor_statistics(hours=24)
            except Exception as e:
                logger.error(f"Error getting sensor statistics: {e}")
                sensor_stats = []

            # Get system overview
            try:
                overview = backend.reports.get_system_overview(hours=24)
            except Exception as e:
                logger.error(f"Error getting system overview: {e}")
                overview = {
                    "total_sensors": 0,
                    "total_readings": 0,
                    "total_alerts": 0,
                    "avg_temperature": 0,
                    "avg_humidity": 0,
                }

            return render_template(
                "dashboard.html",
                overview=overview,
                status=overview,  # Add status for template compatibility
                alerts=active_alerts,
                sensor_statistics=sensor_stats,
                system_health=system_health,
                current_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            )

        elif db and monitoring_core:
            # Fallback to legacy system
            status = monitoring_core.get_system_status()
            active_alerts = db.get_active_alerts()
            stats = db.get_statistics(hours=24)

            return render_template(
                "dashboard.html",
                overview=stats,
                alerts=active_alerts,
                sensor_statistics=[],
                system_health={"health_status": "Legacy Mode"},
                current_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            )
        else:
            # Demo fallback data
            demo_data = {
                "total_sensors": 5,
                "total_readings": 1250,
                "total_alerts": 3,
                "avg_temperature": 3.2,
                "avg_humidity": 85.5,
            }

            demo_alerts = [
                {
                    "id": 1,
                    "device_id": "SENSOR_001",
                    "alert_type": "TEMPERATURE_HIGH",
                    "severity": "HIGH",
                    "message": "Temperature exceeded safe limits",
                    "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                }
            ]

            return render_template(
                "dashboard.html",
                overview=demo_data,
                alerts=demo_alerts,
                sensor_statistics=[],
                system_health={"health_status": "Demo Mode"},
                current_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            )

    except Exception as e:
        logger.error(f"Dashboard error: {e}")
        flash(f"Dashboard error: {e}", "error")
        return render_template(
            "dashboard.html",
            overview={},
            alerts=[],
            sensor_statistics=[],
            system_health={"health_status": "Error"},
            current_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        )


@app.route("/api/dashboard/data")
def dashboard_api():
    """API endpoint for dashboard data"""
    try:
        if backend:
            data = backend.get_dashboard_data()
            return jsonify(data)
        else:
            return jsonify({"error": "Backend not available"}), 503
    except Exception as e:
        logger.error(f"Dashboard API error: {e}")
        return jsonify({"error": str(e)}), 500


# ============================================================================
# ALERTS ROUTES WITH INTEGRATED BACKEND
# ============================================================================


@app.route("/alerts")
def alerts_page():
    """Enhanced alerts page with backend data"""
    try:
        if backend:
            # Get active alerts from backend
            active_alerts = backend.alerts.get_alerts(acknowledged=False, limit=100)

            # Get alert statistics
            alert_stats = backend.alerts.get_alert_statistics(hours=24)

            return render_template(
                "alerts.html", alerts=active_alerts, alert_statistics=alert_stats
            )
        else:
            # Fallback for demo
            demo_alerts = [
                {
                    "id": 1,
                    "device_id": "SENSOR_001",
                    "alert_type": "TEMPERATURE_HIGH",
                    "severity": "HIGH",
                    "message": "Temperature exceeded safe limits",
                    "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "current_value": 6.5,
                    "threshold_value": 4.0,
                }
            ]
            return render_template(
                "alerts.html", alerts=demo_alerts, alert_statistics={}
            )

    except Exception as e:
        logger.error(f"Alerts page error: {e}")
        flash(f"Error loading alerts: {e}", "error")
        return render_template("alerts.html", alerts=[], alert_statistics={})


@app.route("/api/alert/<int:alert_id>/acknowledge", methods=["POST"])
def acknowledge_alert_api(alert_id):
    """Enhanced alert acknowledgment with backend"""
    try:
        if backend:
            data = request.get_json()
            acknowledged_by = data.get("acknowledged_by", "System User")

            success = backend.acknowledge_alert(alert_id, acknowledged_by)

            if success:
                return jsonify({"status": "success", "message": "Alert acknowledged"})
            else:
                return jsonify({"error": "Failed to acknowledge alert"}), 500
        else:
            return jsonify({"error": "Backend not available"}), 503

    except Exception as e:
        logger.error(f"Alert acknowledgment error: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/alerts/create", methods=["POST"])
def create_alert_api():
    """API endpoint to create alerts"""
    try:
        if backend:
            data = request.get_json()

            alert_id = backend.create_sensor_alert(
                device_id=data["device_id"],
                alert_type=data["alert_type"],
                severity=data["severity"],
                message=data["message"],
                current_value=data.get("current_value"),
                threshold_value=data.get("threshold_value"),
            )

            if alert_id:
                return jsonify({"status": "success", "alert_id": alert_id})
            else:
                return jsonify({"error": "Failed to create alert"}), 500
        else:
            return jsonify({"error": "Backend not available"}), 503

    except Exception as e:
        logger.error(f"Alert creation error: {e}")
        return jsonify({"error": str(e)}), 500


# ============================================================================
# SETTINGS ROUTES WITH INTEGRATED BACKEND
# ============================================================================


@app.route("/settings")
def settings_page():
    """Enhanced settings page with comprehensive backend integration"""
    try:
        settings = {}
        sensors = []
        backend_status = "disconnected"

        # Default product types for quick setup
        product_types = [
            {
                "name": "Fresh Meat",
                "description": "Fresh meat products",
                "temp_min": -2,
                "temp_max": 4,
                "humidity_min": 80,
                "humidity_max": 95,
                "shelf_life_hours": 72,
            },
            {
                "name": "Dairy Products",
                "description": "Milk, cheese, yogurt",
                "temp_min": 1,
                "temp_max": 4,
                "humidity_min": 75,
                "humidity_max": 85,
                "shelf_life_hours": 168,
            },
            {
                "name": "Fresh Vegetables",
                "description": "Fresh vegetables and greens",
                "temp_min": 0,
                "temp_max": 8,
                "humidity_min": 85,
                "humidity_max": 95,
                "shelf_life_hours": 120,
            },
            {
                "name": "Frozen Foods",
                "description": "Frozen food products",
                "temp_min": -25,
                "temp_max": -18,
                "humidity_min": 70,
                "humidity_max": 90,
                "shelf_life_hours": 8760,
            },
            {
                "name": "Fruits",
                "description": "Fresh fruits",
                "temp_min": 2,
                "temp_max": 10,
                "humidity_min": 80,
                "humidity_max": 90,
                "shelf_life_hours": 240,
            },
        ]

        if backend:
            try:
                # Get all settings from backend
                settings = backend.settings.get_all_settings()

                # Get sensor configurations
                sensors = backend.settings.get_sensor_config()

                backend_status = "connected"
                logger.info(
                    f"Settings loaded successfully: {len(sensors)} sensors configured"
                )

            except Exception as e:
                logger.error(f"Backend error in settings: {e}")
                backend_status = "error"
                flash(f"Backend connection error: {e}", "warning")
        elif db:
            try:
                # Fallback to legacy database
                sensors = db.get_sensor_config()
                backend_status = "legacy"
                logger.info(
                    f"Settings loaded from legacy database: {len(sensors)} sensors"
                )
            except Exception as e:
                logger.error(f"Legacy database error: {e}")
                backend_status = "error"
                flash(f"Database error: {e}", "error")
        else:
            backend_status = "demo"
            flash("Running in demo mode - no persistent storage", "info")

        return render_template(
            "settings.html",
            settings=settings,
            sensors=sensors,
            product_types=product_types,
            backend_status=backend_status,
        )

    except Exception as e:
        logger.error(f"Settings page error: {e}")
        flash(f"Error loading settings: {e}", "error")
        return render_template(
            "settings.html",
            settings={},
            sensors=[],
            product_types=[],
            backend_status="error",
        )


@app.route("/settings/update", methods=["POST"])
def update_settings():
    """Update system settings using backend"""
    try:
        if backend:
            # Update each setting from the form
            for key, value in request.form.items():
                if key.startswith("setting_"):
                    setting_key = key.replace("setting_", "")
                    success = backend.update_system_setting(
                        setting_key, value, "web_user"
                    )
                    if not success:
                        flash(f"Failed to update setting: {setting_key}", "error")

            flash("Settings updated successfully!", "success")
        else:
            flash("Backend not available", "error")

        return redirect(url_for("settings_page"))
    except Exception as e:
        logger.error(f"Settings update error: {e}")
        flash(f"Error updating settings: {e}", "error")
        return redirect(url_for("settings_page"))


@app.route("/settings/sensor/add", methods=["POST"])
def add_sensor():
    """Add new sensor configuration with comprehensive validation"""
    try:
        # Validate required fields
        required_fields = [
            "device_id",
            "location",
            "temp_min",
            "temp_max",
            "humidity_min",
            "humidity_max",
        ]
        missing_fields = [
            field for field in required_fields if not request.form.get(field)
        ]

        if missing_fields:
            flash(f"Missing required fields: {', '.join(missing_fields)}", "error")
            return redirect(url_for("settings_page"))

        # Validate numeric ranges
        try:
            temp_min = float(request.form["temp_min"])
            temp_max = float(request.form["temp_max"])
            humidity_min = float(request.form["humidity_min"])
            humidity_max = float(request.form["humidity_max"])

            if temp_min >= temp_max:
                flash("Temperature minimum must be less than maximum", "error")
                return redirect(url_for("settings_page"))

            if humidity_min >= humidity_max:
                flash("Humidity minimum must be less than maximum", "error")
                return redirect(url_for("settings_page"))

        except ValueError as e:
            flash(f"Invalid numeric values: {e}", "error")
            return redirect(url_for("settings_page"))

        if backend:
            sensor_config = {
                "device_id": request.form["device_id"].strip(),
                "location": request.form["location"].strip(),
                "product_type": request.form.get("product_type", "").strip(),
                "temp_min": temp_min,
                "temp_max": temp_max,
                "humidity_min": humidity_min,
                "humidity_max": humidity_max,
                "alert_email": request.form.get("alert_email", "").strip(),
                "is_active": "is_active" in request.form,
            }

            success = backend.add_sensor(sensor_config)

            if success:
                flash(
                    f'✅ Sensor {sensor_config["device_id"]} added successfully!',
                    "success",
                )
                logger.info(
                    f"Added sensor: {sensor_config['device_id']} at {sensor_config['location']}"
                )
            else:
                flash("❌ Failed to add sensor configuration", "error")
        elif db:
            # Fallback to legacy database
            try:
                success = db.add_sensor_config(request.form.to_dict())
                if success:
                    flash(
                        f'✅ Sensor {request.form["device_id"]} added (legacy mode)!',
                        "success",
                    )
                else:
                    flash("❌ Failed to add sensor configuration", "error")
            except Exception as e:
                flash(f"Legacy database error: {e}", "error")
        else:
            flash("⚠️ No backend available - sensor not saved", "warning")

        return redirect(url_for("settings_page"))

    except Exception as e:
        logger.error(f"Add sensor error: {e}")
        flash(f"❌ Error adding sensor: {e}", "error")
        return redirect(url_for("settings_page"))


# ============================================================================
# REPORTS ROUTES WITH INTEGRATED BACKEND
# ============================================================================


@app.route("/reports")
def reports_page():
    """Enhanced reports page with backend analytics"""
    try:
        if backend:
            # Get comprehensive reports data from backend
            reports_data = backend.get_reports_data(hours=24)

            # Get sensor statistics for different time periods
            stats_24h = backend.reports.get_system_overview(hours=24)
            stats_7d = backend.reports.get_system_overview(hours=168)
            stats_30d = backend.reports.get_system_overview(hours=720)

            # Get sensor configurations
            sensors = backend.settings.get_sensor_config()

            # Get recent data for preview (simplified)
            recent_data = []

            return render_template(
                "reports.html",
                stats_24h=stats_24h,
                stats_7d=stats_7d,
                stats_30d=stats_30d,
                sensors=sensors,
                recent_data=recent_data,
                performance_report=reports_data.get("performance_report", {}),
            )
        else:
            # Fallback for demo
            return render_template(
                "reports.html",
                stats_24h={},
                stats_7d={},
                stats_30d={},
                sensors=[],
                recent_data=[],
                performance_report={},
            )

    except Exception as e:
        logger.error(f"Reports page error: {e}")
        flash(f"Error loading reports: {e}", "error")
        return render_template(
            "reports.html",
            stats_24h={},
            stats_7d={},
            stats_30d={},
            sensors=[],
            recent_data=[],
            performance_report={},
        )


@app.route("/api/export/data")
def export_data_api():
    """Enhanced data export with backend"""
    try:
        if backend:
            device_id = request.args.get("device_id")
            hours = request.args.get("hours", 24, type=int)
            format_type = request.args.get("format", "csv")

            # Export data using backend
            data = backend.export_data(device_id, hours, format_type)

            if not data:
                return jsonify({"error": "No data available for export"}), 404

            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            device_part = f"_{device_id}" if device_id else "_all"
            filename = f"sensor_data{device_part}_{hours}h_{timestamp}.{format_type}"

            # Return appropriate response
            if format_type == "csv":
                return Response(
                    data,
                    mimetype="text/csv",
                    headers={"Content-Disposition": f"attachment; filename={filename}"},
                )
            else:
                return Response(
                    data,
                    mimetype="application/json",
                    headers={"Content-Disposition": f"attachment; filename={filename}"},
                )
        else:
            return jsonify({"error": "Backend not available"}), 503

    except Exception as e:
        logger.error(f"Data export error: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/reports/performance")
def performance_report_api():
    """API endpoint for performance reports"""
    try:
        if backend:
            hours = request.args.get("hours", 24, type=int)
            device_id = request.args.get("device_id")

            report = backend.reports.generate_performance_report(device_id, hours)
            return jsonify(report)
        else:
            return jsonify({"error": "Backend not available"}), 503

    except Exception as e:
        logger.error(f"Performance report error: {e}")
        return jsonify({"error": str(e)}), 500


# ============================================================================
# SENSOR DATA AND SYSTEM CONTROL ROUTES
# ============================================================================


@app.route("/api/data", methods=["POST"])
def receive_sensor_data():
    """Enhanced API endpoint to receive sensor data with backend integration"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Validate required fields
        required_fields = ["device_id", "temperature", "humidity"]
        if not all(field in data for field in required_fields):
            return jsonify({"error": "Missing required fields"}), 400

        if backend:
            # Check if this reading violates any thresholds and create alerts
            sensor_config = backend.settings.get_sensor_config(data["device_id"])
            if sensor_config:
                temp = data["temperature"]
                humidity = data["humidity"]

                # Check temperature thresholds
                if temp < sensor_config["temp_min"]:
                    backend.create_sensor_alert(
                        device_id=data["device_id"],
                        alert_type="TEMPERATURE_LOW",
                        severity="HIGH",
                        message=f"Temperature {temp}°C is below minimum {sensor_config['temp_min']}°C",
                        current_value=temp,
                        threshold_value=sensor_config["temp_min"],
                    )
                elif temp > sensor_config["temp_max"]:
                    backend.create_sensor_alert(
                        device_id=data["device_id"],
                        alert_type="TEMPERATURE_HIGH",
                        severity="HIGH",
                        message=f"Temperature {temp}°C is above maximum {sensor_config['temp_max']}°C",
                        current_value=temp,
                        threshold_value=sensor_config["temp_max"],
                    )

                # Check humidity thresholds
                if humidity < sensor_config["humidity_min"]:
                    backend.create_sensor_alert(
                        device_id=data["device_id"],
                        alert_type="HUMIDITY_LOW",
                        severity="MEDIUM",
                        message=f"Humidity {humidity}% is below minimum {sensor_config['humidity_min']}%",
                        current_value=humidity,
                        threshold_value=sensor_config["humidity_min"],
                    )
                elif humidity > sensor_config["humidity_max"]:
                    backend.create_sensor_alert(
                        device_id=data["device_id"],
                        alert_type="HUMIDITY_HIGH",
                        severity="MEDIUM",
                        message=f"Humidity {humidity}% is above maximum {sensor_config['humidity_max']}%",
                        current_value=humidity,
                        threshold_value=sensor_config["humidity_max"],
                    )

            # Store the sensor data (you would implement this in your database)
            # For now, we'll just acknowledge receipt
            return (
                jsonify(
                    {"status": "success", "message": "Data received and processed"}
                ),
                200,
            )

        elif monitoring_core:
            # Fallback to legacy processing
            success = monitoring_core.process_sensor_data(data)
            if success:
                return jsonify({"status": "success", "message": "Data received"}), 200
            else:
                return jsonify({"error": "Failed to process data"}), 500
        else:
            return jsonify({"error": "No processing system available"}), 503

    except Exception as e:
        logger.error(f"Sensor data error: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/sensors")
def get_sensors():
    """Get all sensor configurations with backend integration"""
    try:
        if backend:
            sensors = backend.settings.get_sensor_config()
            return jsonify(sensors)
        elif db:
            sensors = db.get_sensor_config()
            return jsonify(sensors)
        else:
            return jsonify({"error": "No data source available"}), 503
    except Exception as e:
        logger.error(f"Get sensors error: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/sensor/<device_id>/data")
def get_sensor_data(device_id):
    """Get data for a specific sensor"""
    try:
        hours = request.args.get("hours", 24, type=int)
        data = db.get_sensor_data(device_id, hours)
        return jsonify(data)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/api/alerts")
def get_alerts():
    """Get active alerts"""
    try:
        alerts = db.get_active_alerts()
        return jsonify(alerts)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/api/alert/<int:alert_id>/acknowledge", methods=["POST"])
def acknowledge_alert(alert_id):
    """Acknowledge an alert"""
    try:
        data = request.get_json()
        acknowledged_by = data.get("acknowledged_by", "Unknown")

        success = db.acknowledge_alert(alert_id, acknowledged_by)

        if success:
            return jsonify({"status": "success"})
        else:
            return jsonify({"error": "Failed to acknowledge alert"}), 500

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/sensors")
def sensors_page():
    """Sensors management page"""
    try:
        sensors = db.get_sensor_config()
        product_types = db.get_product_types()
        return render_template(
            "sensors.html", sensors=sensors, product_types=product_types
        )
    except Exception as e:
        flash(f"Error loading sensors: {str(e)}", "error")
        return render_template("sensors.html", sensors=[], product_types=[])


# Removed duplicate add_sensor function


@app.route("/sensor/<device_id>")
def sensor_detail(device_id):
    """Sensor detail page with charts"""
    try:
        sensor_config = db.get_sensor_config(device_id)
        if not sensor_config:
            flash("Sensor not found", "error")
            return redirect(url_for("sensors_page"))

        # Get recent data for charts
        hours = request.args.get("hours", 24, type=int)
        sensor_data = db.get_sensor_data(device_id, hours)

        # Get statistics
        stats = db.get_statistics(device_id, hours)

        return render_template(
            "sensor_detail.html",
            sensor=sensor_config,
            data=sensor_data,
            stats=stats,
            hours=hours,
        )
    except Exception as e:
        flash(f"Error loading sensor details: {str(e)}", "error")
        return redirect(url_for("sensors_page"))


# Removed duplicate legacy alerts_page function - using the backend version above

# Removed duplicate legacy reports_page function - using the backend version above

# Removed duplicate legacy settings_page and update_settings functions - using the backend versions above

# Removed duplicate legacy add_sensor function - using the backend version above

# ============================================================================
# SYSTEM HEALTH AND CONTROL ROUTES
# ============================================================================


@app.route("/api/system/health")
def system_health_api():
    """Get system health status with backend integration"""
    try:
        if backend:
            health = backend.get_system_health()
            return jsonify(health)
        else:
            return jsonify({"health_status": "Legacy Mode", "health_score": 50})
    except Exception as e:
        logger.error(f"System health error: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/system/cleanup", methods=["POST"])
def system_cleanup_api():
    """Clean up old data with backend integration"""
    try:
        if backend:
            days = request.args.get("days", 30, type=int)
            result = backend.cleanup_old_data(days)
            return jsonify(result)
        else:
            return jsonify({"error": "Backend not available"}), 503
    except Exception as e:
        logger.error(f"System cleanup error: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/system/start", methods=["POST"])
def start_monitoring():
    """Start the monitoring system"""
    try:
        if monitoring_core:
            monitoring_core.start_monitoring()
            return jsonify({"status": "success", "message": "Monitoring started"})
        else:
            return jsonify({"error": "Monitoring core not available"}), 503
    except Exception as e:
        logger.error(f"Start monitoring error: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/system/stop", methods=["POST"])
def stop_monitoring():
    """Stop the monitoring system"""
    try:
        if monitoring_core:
            monitoring_core.stop_monitoring()
            return jsonify({"status": "success", "message": "Monitoring stopped"})
        else:
            return jsonify({"error": "Monitoring core not available"}), 503
    except Exception as e:
        logger.error(f"Stop monitoring error: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/system/status")
def system_status():
    """Get system status with backend integration"""
    try:
        if backend:
            health = backend.get_system_health()
            return jsonify(health)
        elif monitoring_core:
            status = monitoring_core.get_system_status()
            return jsonify(status)
        else:
            return jsonify({"status": "No monitoring system available"}), 503
    except Exception as e:
        logger.error(f"System status error: {e}")
        return jsonify({"error": str(e)}), 500


# ============================================================================
# APPLICATION STARTUP AND SHUTDOWN
# ============================================================================


@app.teardown_appcontext
def close_backend(error):
    """Clean up backend resources"""
    pass


def shutdown_handler():
    """Graceful shutdown handler"""
    logger.info("Shutting down Food Monitoring System...")
    if backend:
        backend.shutdown()
    logger.info("Backend services stopped")


if __name__ == "__main__":
    try:
        print("🍎 Food Monitoring System - Complete Application")
        print("=" * 60)
        print("� Starting integrated frontend and backend system...")
        print()

        if backend:
            print("✅ Backend Services Active:")
            print("   • Settings Manager - System and sensor configuration")
            print("   • Alerts Manager - Real-time alerting and notifications")
            print("   • Reports Manager - Analytics and data export")
        else:
            print("⚠️ Backend services not available - running in legacy mode")

        if monitoring_core:
            print("✅ Legacy monitoring core available")

        print()
        # Try different ports to find an available one
        ports_to_try = [5000, 8080, 3000, 8000, 5001]
        server_started = False

        for port in ports_to_try:
            try:
                print(f"🌐 Trying to start server on port {port}...")
                print(f"   • Dashboard: http://localhost:{port}")
                print(f"   • Alerts: http://localhost:{port}/alerts")
                print(f"   • Reports: http://localhost:{port}/reports")
                print(f"   • Settings: http://localhost:{port}/settings")
                print()
                print("📡 API Endpoints:")
                print(
                    f"   • POST http://localhost:{port}/api/sensor/data - Receive sensor data"
                )
                print(
                    f"   • GET http://localhost:{port}/api/dashboard/data - Dashboard data"
                )
                print(
                    f"   • POST http://localhost:{port}/api/alerts/create - Create alerts"
                )
                print(f"   • GET http://localhost:{port}/api/export/data - Export data")
                print(
                    f"   • GET http://localhost:{port}/api/system/health - System health"
                )
                print("=" * 60)
                print(f"🚀 COPY THIS URL TO YOUR BROWSER: http://localhost:{port}")
                print("=" * 60)
                print("Press Ctrl+C to stop the server")
                print()

                # Start monitoring system if available
                if monitoring_core:
                    try:
                        monitoring_core.start_monitoring()
                        print("✅ Legacy monitoring system started")
                    except Exception as e:
                        print(f"⚠️ Monitoring system error: {e}")

                # Run Flask app with better configuration
                app.run(
                    host="0.0.0.0",  # Listen on all interfaces
                    port=port,
                    debug=False,  # Disable debug for better stability
                    use_reloader=False,  # Disable reloader to prevent issues
                    threaded=True,  # Enable threading for better performance
                )
                server_started = True
                break

            except OSError as e:
                if "Address already in use" in str(e) or "WinError 10048" in str(e):
                    print(f"⚠️ Port {port} is already in use, trying next port...")
                    continue
                else:
                    print(f"❌ Error starting server on port {port}: {e}")
                    continue
            except Exception as e:
                print(f"❌ Unexpected error on port {port}: {e}")
                continue

        if not server_started:
            print("❌ Could not start server on any available port")
            print("Please check if other applications are using these ports")

    except KeyboardInterrupt:
        shutdown_handler()
    except Exception as e:
        logger.error(f"Application error: {e}")
        shutdown_handler()
    finally:
        logger.info("Food Monitoring System stopped")
